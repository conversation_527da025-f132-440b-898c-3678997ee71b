#!/bin/bash
# Matrix Homeserver Podman版本快速启动脚本
# 
# 功能：
# 1. 自动检查Podman环境
# 2. 验证配置文件
# 3. 按正确顺序启动服务
# 4. 执行健康检查
# 5. 提供故障排除建议
#
# 使用方法：
# chmod +x podman-quick-start.sh
# ./podman-quick-start.sh

set -euo pipefail

# ================================================================
# 全局变量定义
# ================================================================

SCRIPT_NAME="Matrix Homeserver Podman快速启动"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }
log_debug() { echo -e "${BLUE}[DEBUG]${NC} $*"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $*"; }

# ================================================================
# 环境检查函数
# ================================================================

check_podman_environment() {
    log_step "检查Podman环境"
    
    # 检查Podman是否安装
    if ! command -v podman >/dev/null 2>&1; then
        log_error "Podman未安装，请先安装Podman"
        echo "Ubuntu/Debian: sudo apt install podman"
        echo "CentOS/RHEL: sudo dnf install podman"
        exit 1
    fi
    
    local podman_version
    podman_version=$(podman --version | cut -d' ' -f3)
    log_info "Podman版本: $podman_version"
    
    # 检查podman-compose是否安装
    if ! command -v podman-compose >/dev/null 2>&1; then
        log_error "podman-compose未安装，请先安装"
        echo "安装命令: pip3 install --user podman-compose"
        exit 1
    fi
    
    local compose_version
    compose_version=$(podman-compose --version 2>/dev/null | cut -d' ' -f3 || echo "未知")
    log_info "podman-compose版本: $compose_version"
    
    # 检查用户权限
    if [[ $EUID -eq 0 ]]; then
        log_warn "正在以root用户运行，建议使用普通用户运行Podman"
    fi
    
    # 检查用户命名空间配置
    if ! grep -q "$(whoami)" /etc/subuid 2>/dev/null; then
        log_error "用户命名空间未配置，请运行以下命令："
        echo "echo \"$(whoami):100000:65536\" | sudo tee -a /etc/subuid"
        echo "echo \"$(whoami):100000:65536\" | sudo tee -a /etc/subgid"
        exit 1
    fi
    
    log_info "Podman环境检查通过"
}

check_configuration() {
    log_step "检查配置文件"
    
    # 检查配置文件是否存在
    if [[ ! -f "deployment.env" ]]; then
        log_error "配置文件不存在: deployment.env"
        log_info "请复制模板文件并编辑:"
        echo "cp podman-deployment.env.template deployment.env"
        echo "nano deployment.env"
        exit 1
    fi
    
    # 加载配置文件
    # shellcheck source=/dev/null
    source deployment.env
    
    # 检查必需的配置项
    local required_vars=(
        "DOMAIN"
        "SUBDOMAIN_MATRIX"
        "DB_PASSWORD"
        "CLOUDFLARE_API_TOKEN"
        "CLOUDFLARE_ZONE_ID"
        "COTURN_SHARED_SECRET"
        "SYNAPSE_FORM_SECRET"
        "SYNAPSE_MACAROON_SECRET"
    )
    
    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必需的配置项:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_info "请编辑 deployment.env 文件并设置这些配置项"
        exit 1
    fi
    
    log_info "配置文件检查通过"
    log_info "域名: $DOMAIN"
    log_info "Matrix服务: ${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT:-8448}"
}

check_network_connectivity() {
    log_step "检查网络连通性"
    
    local test_hosts=(
        "*******"
        "registry-1.docker.io"
        "api.cloudflare.com"
    )
    
    for host in "${test_hosts[@]}"; do
        if ping -c 1 -W 3 "$host" >/dev/null 2>&1; then
            log_info "网络连通性正常: $host"
        else
            log_warn "无法连接到: $host"
        fi
    done
}

# ================================================================
# 服务管理函数
# ================================================================

pull_images() {
    log_step "拉取容器镜像"
    
    if podman-compose pull; then
        log_info "镜像拉取完成"
    else
        log_warn "镜像拉取失败，将使用本地镜像"
    fi
}

start_services() {
    log_step "启动Matrix服务"
    
    # 第一阶段：启动数据库和缓存服务
    log_info "启动数据库和Redis服务..."
    podman-compose up -d db redis
    
    # 等待数据库启动
    log_info "等待数据库启动完成..."
    local max_wait=60
    local wait_time=0
    
    while [[ $wait_time -lt $max_wait ]]; do
        if podman exec matrix_postgres pg_isready -U "${DB_USER:-synapse}" >/dev/null 2>&1; then
            log_info "数据库启动完成"
            break
        fi
        
        sleep 5
        wait_time=$((wait_time + 5))
        echo -n "."
    done
    echo
    
    if [[ $wait_time -ge $max_wait ]]; then
        log_error "数据库启动超时"
        exit 1
    fi
    
    # 第二阶段：启动Synapse服务
    log_info "启动Synapse服务..."
    podman-compose up -d synapse
    
    # 等待Synapse启动
    log_info "等待Synapse启动完成..."
    max_wait=120
    wait_time=0
    
    while [[ $wait_time -lt $max_wait ]]; do
        if curl -s --max-time 5 http://localhost:8008/_matrix/client/versions >/dev/null 2>&1; then
            log_info "Synapse启动完成"
            break
        fi
        
        sleep 10
        wait_time=$((wait_time + 10))
        echo -n "."
    done
    echo
    
    if [[ $wait_time -ge $max_wait ]]; then
        log_error "Synapse启动超时"
        exit 1
    fi
    
    # 第三阶段：启动代理和TURN服务
    log_info "启动Nginx和Coturn服务..."
    podman-compose up -d nginx coturn
    
    log_info "所有服务启动完成"
}

check_service_health() {
    log_step "检查服务健康状态"
    
    # 检查容器状态
    echo "容器状态:"
    podman-compose ps
    echo
    
    # 检查服务端口
    local services=(
        "matrix_postgres:5432"
        "matrix_redis:6379"
        "matrix_synapse:8008"
        "matrix_nginx:${HTTPS_PORT:-8448}"
    )
    
    for service_port in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"
        
        if podman exec "$service" netstat -tln 2>/dev/null | grep -q ":$port "; then
            log_info "$service 端口 $port 正常监听"
        else
            log_warn "$service 端口 $port 未监听"
        fi
    done
    
    # 检查Matrix API
    local matrix_url="https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT:-8448}"
    if curl -k -s --max-time 10 "${matrix_url}/_matrix/client/versions" >/dev/null 2>&1; then
        log_info "Matrix API响应正常"
    else
        log_warn "Matrix API无响应，可能需要配置DNS和证书"
    fi
}

# ================================================================
# 主函数
# ================================================================

show_banner() {
    echo -e "${CYAN}"
    echo "================================================================"
    echo "           Matrix Homeserver Podman快速启动脚本"
    echo "================================================================"
    echo -e "${NC}"
    echo "版本: $SCRIPT_VERSION"
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "用户: $(whoami)"
    echo "目录: $(pwd)"
    echo
}

show_completion_info() {
    echo
    echo -e "${GREEN}================================================================"
    echo "                    部署完成！"
    echo "================================================================${NC}"
    echo
    echo "服务信息:"
    echo "  域名: ${DOMAIN}"
    echo "  Matrix服务: https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT:-8448}"
    echo "  管理员用户: @${SYNAPSE_ADMIN_USER:-admin}:${DOMAIN}"
    echo
    echo "下一步操作:"
    echo "  1. 创建管理员用户:"
    echo "     podman exec -it matrix_synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008"
    echo
    echo "  2. 测试服务:"
    echo "     curl -k https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT:-8448}/_matrix/client/versions"
    echo
    echo "  3. 查看日志:"
    echo "     podman-compose logs -f"
    echo
    echo "  4. 停止服务:"
    echo "     podman-compose down"
    echo
    echo "  5. 重启服务:"
    echo "     podman-compose restart"
    echo
    echo "配置文件位置:"
    echo "  - 主配置: $(pwd)/deployment.env"
    echo "  - Compose: $(pwd)/podman-docker-compose.yml"
    echo "  - 数据目录: $(pwd)/data/"
    echo
}

main() {
    show_banner
    
    # 环境检查
    check_podman_environment
    check_configuration
    check_network_connectivity
    
    # 服务部署
    pull_images
    start_services
    
    # 健康检查
    check_service_health
    
    # 显示完成信息
    show_completion_info
}

# ================================================================
# 脚本入口
# ================================================================

# 检查是否在正确的目录中
if [[ ! -f "podman-docker-compose.yml" ]]; then
    log_error "请在包含 podman-docker-compose.yml 的目录中运行此脚本"
    exit 1
fi

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --help    显示此帮助信息"
        echo "  --check   仅执行环境检查"
        echo "  --pull    仅拉取镜像"
        echo "  --start   仅启动服务"
        echo "  --status  仅检查状态"
        exit 0
        ;;
    --check)
        show_banner
        check_podman_environment
        check_configuration
        check_network_connectivity
        ;;
    --pull)
        pull_images
        ;;
    --start)
        start_services
        ;;
    --status)
        check_service_health
        ;;
    "")
        main
        ;;
    *)
        log_error "未知参数: $1"
        echo "使用 --help 查看帮助信息"
        exit 1
        ;;
esac
