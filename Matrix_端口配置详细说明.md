# Matrix Homeserver 端口配置详细说明

## 📋 端口协议类型修改总结

已成功修改 `Matrix_Homeserver_手动部署指南_技术新手版.md` 文件中的端口规划部分，现在所有端口都明确标注了协议类型（TCP/UDP）。

## 🔌 完整端口配置表

### 对外开放端口（需要路由器端口转发）

| 端口 | 协议 | 服务 | 用途说明 | 访问范围 | 必需性 |
|------|------|------|----------|----------|--------|
| **8448** | **TCP** | Nginx | Matrix HTTPS服务，客户端连接和联邦通信 | 公网访问 | 必需 |
| **3478** | **TCP** | Coturn | STUN/TURN服务，TCP连接建立 | 公网访问 | 必需 |
| **3478** | **UDP** | Coturn | STUN/TURN服务，UDP连接建立 | 公网访问 | 必需 |
| **5349** | **TCP** | Coturn | TURNS服务，TLS加密的TURN连接 | 公网访问 | 推荐 |
| **49152-65535** | **UDP** | Coturn | TURN UDP端口范围，音视频数据传输 | 公网访问 | 必需 |

### 内网专用端口（仅容器间通信）

| 端口 | 协议 | 服务 | 用途说明 | 访问范围 | 必需性 |
|------|------|------|----------|----------|--------|
| **8008** | **TCP** | Synapse | Matrix服务器内部HTTP接口 | 仅内网 | 必需 |
| **5432** | **TCP** | PostgreSQL | 数据库连接 | 仅内网 | 必需 |
| **6379** | **TCP** | Redis | 缓存服务连接 | 仅内网 | 必需 |

## 🛡️ 防火墙配置

### 内网服务器防火墙规则

```bash
# 允许SSH（请根据实际SSH端口修改）
sudo ufw allow 22/tcp

# 允许Matrix服务端口
sudo ufw allow 8448/tcp comment 'Matrix HTTPS'
sudo ufw allow 3478/tcp comment 'STUN TCP'
sudo ufw allow 3478/udp comment 'STUN UDP'
sudo ufw allow 5349/tcp comment 'TURNS'
sudo ufw allow 49152:65535/udp comment 'TURN UDP Range'
```

### 外部VPS防火墙规则

```bash
# 允许SSH
sudo ufw allow 22/tcp

# 允许HTTP和HTTPS
sudo ufw allow 80/tcp comment 'HTTP'
sudo ufw allow 443/tcp comment 'HTTPS'
```

## 🌐 路由器端口转发配置

### 端口转发规则表

| 外部端口 | 内部端口 | 协议 | 目标IP | 说明 |
|---------|---------|------|--------|------|
| 8448 | 8448 | TCP | ************* | Matrix HTTPS服务 |
| 3478 | 3478 | TCP | ************* | STUN TCP连接 |
| 3478 | 3478 | UDP | ************* | STUN UDP连接 |
| 5349 | 5349 | TCP | ************* | TURNS服务 |
| 49152-65535 | 49152-65535 | UDP | ************* | TURN UDP端口范围 |

### RouterOS配置命令

```bash
/ip firewall nat
add action=dst-nat chain=dstnat dst-port=8448 protocol=tcp to-addresses=************* to-ports=8448
add action=dst-nat chain=dstnat dst-port=3478 protocol=tcp to-addresses=************* to-ports=3478
add action=dst-nat chain=dstnat dst-port=3478 protocol=udp to-addresses=************* to-ports=3478
add action=dst-nat chain=dstnat dst-port=5349 protocol=tcp to-addresses=************* to-ports=5349
add action=dst-nat chain=dstnat dst-port=49152-65535 protocol=udp to-addresses=************* to-ports=49152-65535
```

## 📊 端口用途详细解释

### Matrix核心服务端口

**8448/TCP - Matrix HTTPS服务**
- **用途**: Matrix客户端连接和服务器联邦通信
- **协议**: HTTPS (HTTP over TLS)
- **重要性**: 核心端口，必须开放
- **标准**: Matrix官方标准端口，IANA注册为`matrix-fed`

### Coturn音视频服务端口

**3478/TCP - STUN/TURN TCP**
- **用途**: STUN/TURN服务的TCP连接
- **协议**: STUN/TURN over TCP
- **重要性**: 音视频通话必需
- **标准**: RFC 5389 (STUN), RFC 5766 (TURN)

**3478/UDP - STUN/TURN UDP**
- **用途**: STUN/TURN服务的UDP连接
- **协议**: STUN/TURN over UDP
- **重要性**: 音视频通话必需，UDP性能更好
- **标准**: RFC 5389 (STUN), RFC 5766 (TURN)

**5349/TCP - TURNS**
- **用途**: TLS加密的TURN连接
- **协议**: TURN over TLS
- **重要性**: 安全的音视频通话
- **标准**: RFC 5766 (TURN over TLS)

**49152-65535/UDP - TURN UDP范围**
- **用途**: 音视频数据传输的UDP端口范围
- **协议**: RTP/SRTP over UDP
- **重要性**: 音视频通话数据传输必需
- **说明**: 动态分配，需要开放整个范围

### 内部服务端口

**8008/TCP - Synapse内部HTTP**
- **用途**: Synapse Matrix服务器内部HTTP接口
- **协议**: HTTP
- **访问**: 仅容器间通信，不对外开放
- **重要性**: 核心服务通信

**5432/TCP - PostgreSQL**
- **用途**: PostgreSQL数据库连接
- **协议**: PostgreSQL Wire Protocol
- **访问**: 仅容器间通信，不对外开放
- **重要性**: 数据存储核心

**6379/TCP - Redis**
- **用途**: Redis缓存服务连接
- **协议**: Redis Protocol (RESP)
- **访问**: 仅容器间通信，不对外开放
- **重要性**: 性能优化

## 🔍 端口测试命令

### 测试端口开放状态

```bash
# 测试TCP端口
telnet your-domain.com 8448
telnet your-domain.com 3478
telnet your-domain.com 5349

# 测试UDP端口（需要专用工具）
nc -u your-domain.com 3478

# 检查本地端口监听
sudo netstat -tlnp | grep -E "(8448|3478|5349|8008|5432|6379)"
sudo ss -tlnp | grep -E "(8448|3478|5349|8008|5432|6379)"
```

### 防火墙状态检查

```bash
# 查看UFW状态
sudo ufw status verbose

# 查看iptables规则
sudo iptables -L -n -v

# 查看端口占用
sudo lsof -i :8448
sudo lsof -i :3478
```

## 🚨 安全注意事项

### 端口安全配置

1. **最小权限原则**
   - 只开放必需的端口
   - 内部服务端口不对外开放
   - 定期审查端口开放状态

2. **访问控制**
   - 使用防火墙限制访问来源
   - 考虑使用fail2ban防护
   - 监控异常连接

3. **端口范围管理**
   - TURN UDP端口范围较大，注意安全
   - 可以考虑缩小端口范围（如49152-49252）
   - 定期检查端口使用情况

### 网络安全建议

1. **定期更新**
   - 保持系统和服务更新
   - 及时应用安全补丁

2. **监控告警**
   - 监控端口连接状态
   - 设置异常流量告警

3. **备份恢复**
   - 定期备份配置
   - 测试恢复流程

## 📝 配置检查清单

### 部署前检查

- [ ] 确认所有端口协议类型正确
- [ ] 验证路由器端口转发配置
- [ ] 检查防火墙规则设置
- [ ] 测试端口连通性

### 部署后验证

- [ ] 验证Matrix服务可访问（8448/TCP）
- [ ] 测试STUN/TURN服务（3478/TCP+UDP）
- [ ] 检查TURNS服务（5349/TCP）
- [ ] 验证音视频通话功能
- [ ] 确认内部服务正常通信

通过这个详细的端口配置说明，用户可以清楚地了解每个端口的协议类型、用途和配置要求，避免因端口配置错误导致的服务异常。
