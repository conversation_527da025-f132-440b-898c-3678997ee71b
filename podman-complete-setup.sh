#!/bin/bash
# Matrix Homeserver Podman完整安装脚本
# 
# 功能：
# 1. 自动安装Podman环境
# 2. 创建项目结构
# 3. 生成所有配置文件
# 4. 一键部署Matrix服务
#
# 使用方法：
# chmod +x podman-complete-setup.sh
# ./podman-complete-setup.sh

set -euo pipefail

# ================================================================
# 全局变量定义
# ================================================================

SCRIPT_NAME="Matrix Homeserver Podman完整安装"
SCRIPT_VERSION="1.0.0"
PROJECT_DIR="/opt/matrix"
MATRIX_USER="matrix"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $*"; }
log_success() { echo -e "${CYAN}[SUCCESS]${NC} $*"; }

# ================================================================
# 显示横幅
# ================================================================
show_banner() {
    echo -e "${CYAN}"
    echo "================================================================"
    echo "           Matrix Homeserver Podman完整安装脚本"
    echo "================================================================"
    echo -e "${NC}"
    echo "版本: $SCRIPT_VERSION"
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "目标目录: $PROJECT_DIR"
    echo
}

# ================================================================
# 检查系统要求
# ================================================================
check_system_requirements() {
    log_step "检查系统要求"
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测操作系统"
        exit 1
    fi
    
    source /etc/os-release
    log_info "操作系统: $PRETTY_NAME"
    
    # 检查架构
    local arch=$(uname -m)
    if [[ "$arch" != "x86_64" ]]; then
        log_error "不支持的架构: $arch (需要 x86_64)"
        exit 1
    fi
    log_info "系统架构: $arch"
    
    # 检查内存
    local memory_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $memory_gb -lt 4 ]]; then
        log_warn "内存不足 ${memory_gb}GB (推荐至少4GB)"
    else
        log_info "系统内存: ${memory_gb}GB"
    fi
    
    # 检查磁盘空间
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $disk_gb -lt 50 ]]; then
        log_warn "磁盘空间不足 ${disk_gb}GB (推荐至少50GB)"
    else
        log_info "可用磁盘: ${disk_gb}GB"
    fi
}

# ================================================================
# 安装系统依赖
# ================================================================
install_system_dependencies() {
    log_step "安装系统依赖"
    
    # 更新软件包列表
    log_info "更新软件包列表..."
    sudo apt update
    
    # 安装基础工具
    log_info "安装基础工具..."
    sudo apt install -y \
        curl wget git nano vim unzip htop \
        dnsutils net-tools iputils-ping \
        ufw fail2ban \
        build-essential python3 python3-pip \
        jq tree
    
    log_success "系统依赖安装完成"
}

# ================================================================
# 创建Matrix用户
# ================================================================
create_matrix_user() {
    log_step "创建Matrix用户"
    
    # 检查用户是否已存在
    if id "$MATRIX_USER" &>/dev/null; then
        log_info "用户 $MATRIX_USER 已存在"
    else
        log_info "创建用户 $MATRIX_USER..."
        sudo useradd -m -s /bin/bash "$MATRIX_USER"
        sudo usermod -aG sudo "$MATRIX_USER"
        log_success "用户 $MATRIX_USER 创建完成"
        
        log_warn "请为用户 $MATRIX_USER 设置密码："
        sudo passwd "$MATRIX_USER"
    fi
}

# ================================================================
# 安装Podman
# ================================================================
install_podman() {
    log_step "安装Podman"
    
    # 检查Podman是否已安装
    if command -v podman >/dev/null 2>&1; then
        local version=$(podman --version | cut -d' ' -f3)
        log_info "Podman已安装，版本: $version"
    else
        log_info "安装Podman..."
        sudo apt install -y podman
        log_success "Podman安装完成"
    fi
    
    # 配置用户命名空间
    log_info "配置用户命名空间..."
    if ! grep -q "$MATRIX_USER" /etc/subuid 2>/dev/null; then
        echo "$MATRIX_USER:100000:65536" | sudo tee -a /etc/subuid
        echo "$MATRIX_USER:100000:65536" | sudo tee -a /etc/subgid
        log_success "用户命名空间配置完成"
    else
        log_info "用户命名空间已配置"
    fi
}

# ================================================================
# 安装podman-compose
# ================================================================
install_podman_compose() {
    log_step "安装podman-compose"
    
    # 切换到matrix用户执行
    sudo -u "$MATRIX_USER" bash << 'EOF'
    # 检查是否已安装
    if command -v podman-compose >/dev/null 2>&1; then
        echo "podman-compose已安装"
    else
        echo "安装podman-compose..."
        pip3 install --user podman-compose
        
        # 添加到PATH
        if ! grep -q '.local/bin' ~/.bashrc; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
        fi
    fi
EOF
    
    log_success "podman-compose安装完成"
}

# ================================================================
# 创建项目结构
# ================================================================
create_project_structure() {
    log_step "创建项目结构"
    
    # 创建项目根目录
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown "$MATRIX_USER:$MATRIX_USER" "$PROJECT_DIR"
    
    # 切换到matrix用户创建目录结构
    sudo -u "$MATRIX_USER" bash << EOF
    cd "$PROJECT_DIR"
    
    # 创建目录结构
    mkdir -p {config,data,logs,scripts,backup}
    mkdir -p data/{postgres,redis,synapse,nginx,coturn}
    mkdir -p data/{nginx/{conf,certs,logs},coturn/{conf,certs,logs}}
    mkdir -p data/synapse/{media,logs}
    
    # 设置权限
    chmod -R 755 .
    chmod -R 750 data
EOF
    
    log_success "项目结构创建完成"
}

# ================================================================
# 配置防火墙
# ================================================================
configure_firewall() {
    log_step "配置防火墙"
    
    # 启用UFW
    sudo ufw --force enable
    
    # 配置防火墙规则
    sudo ufw allow ssh
    sudo ufw allow 8448/tcp comment "Matrix HTTPS"
    sudo ufw allow 3478/tcp comment "STUN/TURN TCP"
    sudo ufw allow 3478/udp comment "STUN/TURN UDP"
    sudo ufw allow 5349/tcp comment "TURNS"
    sudo ufw allow 49152:65535/udp comment "TURN UDP range"
    
    log_success "防火墙配置完成"
}

# ================================================================
# 生成配置文件
# ================================================================
generate_config_files() {
    log_step "生成配置文件"
    
    # 生成强密码
    local db_password=$(openssl rand -base64 32)
    local coturn_secret=$(openssl rand -base64 32)
    local form_secret=$(openssl rand -base64 32)
    local macaroon_secret=$(openssl rand -base64 32)
    
    # 切换到matrix用户生成配置文件
    sudo -u "$MATRIX_USER" bash << EOF
    cd "$PROJECT_DIR"
    
    # 创建环境配置文件
    cat > config/deployment.env << 'ENVEOF'
# Matrix Homeserver Podman版本配置文件
# 请根据实际情况修改以下配置

# ================================================================
# 基础域名配置（必须修改）
# ================================================================
DOMAIN="example.com"
SUBDOMAIN_MATRIX="matrix"
HTTPS_PORT="8448"

# ================================================================
# 数据库配置
# ================================================================
DB_NAME="synapse"
DB_USER="synapse"
DB_PASSWORD="$db_password"

# ================================================================
# Cloudflare DNS配置（必须配置）
# ================================================================
CLOUDFLARE_API_TOKEN="your_cloudflare_api_token_here"
CLOUDFLARE_ZONE_ID="your_cloudflare_zone_id_here"
CLOUDFLARE_EMAIL="<EMAIL>"

# ================================================================
# TURN服务器配置
# ================================================================
COTURN_SHARED_SECRET="$coturn_secret"
TURN_PORT="3478"
TURNS_PORT="5349"
COTURN_MIN_PORT="49152"
COTURN_MAX_PORT="65535"

# ================================================================
# Synapse配置
# ================================================================
SYNAPSE_ADMIN_USER="admin"
SYNAPSE_FORM_SECRET="$form_secret"
SYNAPSE_MACAROON_SECRET="$macaroon_secret"
ENABLE_REGISTRATION="false"
MAX_UPLOAD_SIZE="50M"

# ================================================================
# 容器资源限制
# ================================================================
POSTGRES_MEMORY_LIMIT="1g"
POSTGRES_CPU_LIMIT="1"
REDIS_MEMORY_LIMIT="512m"
REDIS_CPU_LIMIT="0.5"
SYNAPSE_MEMORY_LIMIT="2g"
SYNAPSE_CPU_LIMIT="2"
NGINX_MEMORY_LIMIT="256m"
NGINX_CPU_LIMIT="0.5"
COTURN_MEMORY_LIMIT="512m"
COTURN_CPU_LIMIT="1"
ENVEOF

    # 显示生成的密码
    echo
    echo "=== 生成的密码信息 ==="
    echo "数据库密码: $db_password"
    echo "Coturn密钥: $coturn_secret"
    echo "Synapse表单密钥: $form_secret"
    echo "Synapse Macaroon密钥: $macaroon_secret"
    echo
    echo "请保存这些密码信息！"
    echo "配置文件位置: $PROJECT_DIR/config/deployment.env"
    echo
EOF
    
    log_success "配置文件生成完成"
}

# ================================================================
# 主函数
# ================================================================
main() {
    show_banner
    
    # 检查是否为root用户
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
    
    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_error "需要sudo权限，请确保当前用户在sudo组中"
        exit 1
    fi
    
    # 执行安装步骤
    check_system_requirements
    install_system_dependencies
    create_matrix_user
    install_podman
    install_podman_compose
    create_project_structure
    configure_firewall
    generate_config_files
    
    # 显示完成信息
    echo
    log_success "Matrix Homeserver Podman环境安装完成！"
    echo
    echo "下一步操作："
    echo "1. 编辑配置文件: sudo nano $PROJECT_DIR/config/deployment.env"
    echo "2. 修改域名和Cloudflare配置"
    echo "3. 切换到matrix用户: sudo su - $MATRIX_USER"
    echo "4. 进入项目目录: cd $PROJECT_DIR"
    echo "5. 运行部署脚本: ./scripts/deploy.sh"
    echo
    echo "详细部署指南请参考: Matrix_Homeserver_Podman完整部署指南_小白专用.md"
    echo
}

# 运行主函数
main "$@"
