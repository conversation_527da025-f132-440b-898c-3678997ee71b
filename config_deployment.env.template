# ================================================================
# Matrix Homeserver 部署配置模板
# ================================================================
# 说明：复制此文件为 deployment.env 并根据实际情况修改配置
# 版本：2.0.0 (更新为Matrix官方标准8448端口)
# ================================================================

# ================================================================
# 基础域名配置
# ================================================================

# 主域名：你拥有的域名，用于Matrix服务器名称和外部指路牌服务
# 例如：example.com
# 作用：作为Matrix服务器的标识符，客户端通过这个域名发现你的服务器
DOMAIN="example.com"

# Matrix服务子域名：实际提供Matrix服务的子域名
# 例如：matrix（完整域名为matrix.example.com）
# 作用：客户端和联邦服务器通过这个地址访问你的Matrix服务
SUBDOMAIN_MATRIX="matrix"

# HTTPS服务端口：Matrix官方标准联邦端口
# 标准值：8448（Matrix官方标准端口，IANA注册为matrix-fed）
# 作用：Nginx反向代理监听的端口，需要在路由器上配置端口转发
# 优势：完全符合Matrix规范，确保最佳兼容性
HTTPS_PORT="8448"

# ================================================================
# 数据库配置
# ================================================================

# 数据库名称：PostgreSQL数据库名
# 默认值：synapse
# 作用：存储Matrix所有数据（用户、房间、消息等）
DB_NAME="synapse"

# 数据库用户名：PostgreSQL用户名
# 默认值：synapse
# 作用：Synapse连接数据库使用的用户账户
DB_USER="synapse"

# 数据库密码：PostgreSQL用户密码
# 生成方式：openssl rand -base64 32
# 安全要求：至少32位随机字符，包含大小写字母、数字和特殊字符
# 作用：保护数据库访问安全
DB_PASSWORD="your_secure_database_password_here"

# 数据库主机：在Docker环境中使用容器名
# 默认值：db（对应docker-compose.yml中的服务名）
# 作用：Synapse通过这个地址连接PostgreSQL容器
DB_HOST="db"

# 数据库端口：PostgreSQL默认端口
# 默认值：5432
# 作用：数据库服务监听的端口
DB_PORT="5432"

# ================================================================
# Redis缓存配置
# ================================================================

# Redis主机：在Docker环境中使用容器名
# 默认值：redis（对应docker-compose.yml中的服务名）
# 作用：Synapse通过这个地址连接Redis容器进行缓存操作
REDIS_HOST="redis"

# Redis端口：Redis默认端口
# 默认值：6379
# 作用：Redis服务监听的端口
REDIS_PORT="6379"

# Redis密码：如果Redis启用了认证
# 可选配置：如果不设置密码，Redis将不启用认证
# 安全建议：生产环境建议设置Redis密码
# REDIS_PASSWORD="your_redis_password"

# ================================================================
# Cloudflare DNS API配置
# ================================================================

# Cloudflare API令牌：用于自动管理DNS记录
# 获取方式：
# 1. 登录Cloudflare控制台
# 2. 进入"我的个人资料" → "API令牌"
# 3. 创建自定义令牌，权限设置：
#    - 区域:区域:读取
#    - 区域:DNS:编辑
# 4. 区域资源：包含特定区域 - 选择你的域名
# 作用：允许脚本自动更新DNS记录，实现动态IP同步
CLOUDFLARE_API_TOKEN="your_cloudflare_api_token_here"

# Cloudflare Zone ID：域名区域标识符
# 获取方式：在Cloudflare域名管理页面右侧可以找到
# 作用：指定要操作的DNS区域
CLOUDFLARE_ZONE_ID="your_cloudflare_zone_id_here"

# ================================================================
# TURN/STUN服务器配置
# ================================================================

# Coturn共享密钥：用于TURN服务器认证
# 生成方式：openssl rand -base64 32
# 安全要求：与Synapse配置中的turn_shared_secret保持一致
# 作用：Matrix客户端通过这个密钥获取TURN服务访问权限
COTURN_SHARED_SECRET="your_coturn_shared_secret_here"

# STUN/TURN端口：标准STUN/TURN服务端口
# 默认值：3478
# 协议：TCP和UDP
# 作用：客户端通过这个端口进行NAT穿透和媒体中继
TURN_PORT="3478"

# TURNS端口：加密的TURN服务端口
# 默认值：5349
# 协议：TCP over TLS
# 作用：提供加密的TURN服务，增强安全性
TURNS_PORT="5349"

# TURN UDP端口范围：用于媒体数据传输的UDP端口范围
# 开始端口：49152（IANA建议的动态端口范围开始）
# 结束端口：65535（最大端口号）
# 作用：Coturn在这个范围内分配端口进行媒体数据中继
# 注意：需要在路由器上开放这个UDP端口范围
COTURN_MIN_PORT="49152"
COTURN_MAX_PORT="65535"

# ================================================================
# Synapse安全配置
# ================================================================

# 管理员用户名：Matrix服务器的管理员账户
# 默认值：admin
# 作用：拥有服务器管理权限的用户账户
SYNAPSE_ADMIN_USER="admin"

# 表单密钥：用于CSRF（跨站请求伪造）保护
# 生成方式：openssl rand -base64 32
# 作用：保护Web表单免受CSRF攻击
SYNAPSE_FORM_SECRET="your_synapse_form_secret_here"

# Macaroon密钥：用于生成访问令牌
# 生成方式：openssl rand -base64 32
# 作用：用于生成和验证用户访问令牌
SYNAPSE_MACAROON_SECRET="your_synapse_macaroon_secret_here"

# ================================================================
# RouterOS API配置（可选）
# ================================================================

# RouterOS路由器IP地址：MikroTik路由器的内网IP
# 默认值：***********（根据实际网络环境调整）
# 作用：IP监控脚本通过这个地址连接路由器获取WAN IP
ROUTEROS_HOST="***********"

# RouterOS API端口：RouterOS API服务端口
# 默认值：8728
# 作用：API连接使用的端口
ROUTEROS_PORT="8728"

# RouterOS API用户名：用于API访问的用户账户
# 安全建议：创建专用的API用户，不要使用admin账户
# 权限要求：至少需要read权限来获取接口信息
ROUTEROS_USER="matrix-api"

# RouterOS API密码：API用户的密码
# 安全要求：使用强密码
# 作用：API认证使用
ROUTEROS_PASSWORD="your_routeros_password_here"

# WAN接口名称：路由器的WAN接口名称
# 默认值：WAN（RouterOS默认WAN接口名）
# 可能的值：ether1, pppoe-out1, WAN等
# 作用：指定要监控IP地址的网络接口
ROUTEROS_WAN_INTERFACE="WAN"

# ================================================================
# 资源限制配置
# ================================================================

# PostgreSQL内存限制：数据库容器的内存使用上限
# 推荐值：1g（1GB）
# 调整原则：根据服务器总内存的20-30%设置
POSTGRES_MEMORY_LIMIT="1g"

# PostgreSQL CPU限制：数据库容器的CPU使用上限
# 推荐值：1（1个CPU核心）
# 调整原则：根据服务器CPU核心数和负载情况调整
POSTGRES_CPU_LIMIT="1"

# Redis内存限制：缓存容器的内存使用上限
# 推荐值：512m（512MB）
# 调整原则：根据缓存需求和可用内存调整
REDIS_MEMORY_LIMIT="512m"

# Redis CPU限制：缓存容器的CPU使用上限
# 推荐值：0.5（半个CPU核心）
# 调整原则：Redis通常不需要太多CPU资源
REDIS_CPU_LIMIT="0.5"

# Synapse内存限制：Matrix服务器容器的内存使用上限
# 推荐值：2g（2GB）
# 调整原则：这是最重要的服务，分配较多内存
SYNAPSE_MEMORY_LIMIT="2g"

# Synapse CPU限制：Matrix服务器容器的CPU使用上限
# 推荐值：2（2个CPU核心）
# 调整原则：根据用户数量和活跃度调整
SYNAPSE_CPU_LIMIT="2"

# Nginx内存限制：反向代理容器的内存使用上限
# 推荐值：256m（256MB）
# 调整原则：Nginx内存使用较少，256MB通常足够
NGINX_MEMORY_LIMIT="256m"

# Nginx CPU限制：反向代理容器的CPU使用上限
# 推荐值：0.5（半个CPU核心）
# 调整原则：Nginx CPU使用较少
NGINX_CPU_LIMIT="0.5"

# Coturn内存限制：TURN服务器容器的内存使用上限
# 推荐值：512m（512MB）
# 调整原则：根据同时进行音视频通话的用户数量调整
COTURN_MEMORY_LIMIT="512m"

# Coturn CPU限制：TURN服务器容器的CPU使用上限
# 推荐值：1（1个CPU核心）
# 调整原则：音视频中继需要一定的CPU资源
COTURN_CPU_LIMIT="1"

# ================================================================
# 日志和监控配置
# ================================================================

# 日志级别：系统日志的详细程度
# 可选值：DEBUG, INFO, WARN, ERROR
# 推荐值：INFO（生产环境）, DEBUG（调试时）
# 作用：控制日志输出的详细程度
LOG_LEVEL="INFO"

# 日志保留天数：日志文件的保留时间
# 推荐值：30（30天）
# 作用：自动清理过期的日志文件，节省磁盘空间
LOG_RETENTION_DAYS="30"

# 备份保留天数：备份文件的保留时间
# 推荐值：90（90天）
# 作用：自动清理过期的备份文件
BACKUP_RETENTION_DAYS="90"

# ================================================================
# 环境标识
# ================================================================

# 部署环境：标识当前部署的环境类型
# 可选值：development, staging, production
# 作用：用于区分不同的部署环境
ENVIRONMENT="production"

# 部署版本：当前部署的版本号
# 格式：语义化版本号（如2.0.0）
# 作用：版本管理和问题追踪
DEPLOYMENT_VERSION="2.0.0"

# 最后更新时间：配置文件的最后更新时间
# 格式：ISO 8601格式的UTC时间
# 作用：记录配置变更时间
LAST_UPDATED="$(date -u +%Y-%m-%dT%H:%M:%SZ)"

# ================================================================
# 配置说明
# ================================================================
# 
# 重要提示：
# 1. 请将所有 "your_*_here" 占位符替换为实际值
# 2. 使用 openssl rand -base64 32 生成强密码
# 3. 确保Cloudflare API Token有正确的权限
# 4. 根据服务器配置调整资源限制
# 5. 保存此文件为 deployment.env 并设置适当权限：chmod 600 deployment.env
#
# Matrix官方8448端口说明：
# - 8448是Matrix联邦协议的官方标准端口
# - 已在IANA注册为matrix-fed服务
# - 确保与所有Matrix实现的最佳兼容性
# - 通过.well-known文件重定向机制完美支持
#
# ================================================================
