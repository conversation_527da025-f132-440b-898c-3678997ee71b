# Matrix Homeserver Podman 快速参考卡

## 🚀 常用命令

### Pod管理
```bash
# 查看服务状态
./scripts/manage-pod.sh status

# 启动所有服务
./scripts/manage-pod.sh start

# 停止所有服务
./scripts/manage-pod.sh stop

# 重启所有服务
./scripts/manage-pod.sh restart

# 完整重新部署
./scripts/manage-pod.sh deploy
```

### 日志查看
```bash
# 查看所有容器状态
podman ps --pod

# 查看特定服务日志
./scripts/manage-pod.sh logs postgres
./scripts/manage-pod.sh logs redis
./scripts/manage-pod.sh logs synapse
./scripts/manage-pod.sh logs nginx
./scripts/manage-pod.sh logs coturn

# 实时跟踪日志
podman logs -f matrix-synapse
```

### 容器操作
```bash
# 进入容器
podman exec -it matrix-synapse /bin/bash
podman exec -it matrix-postgres /bin/bash

# 重启单个容器
podman restart matrix-synapse

# 查看容器资源使用
podman stats
```

## 🔧 维护操作

### 用户管理
```bash
# 创建新用户
podman exec -it matrix-synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008

# 停用用户
podman exec -it matrix-synapse python -m synapse.app.admin_cmd deactivate-user @username:example.com

# 重置用户密码
podman exec -it matrix-synapse python -m synapse.app.admin_cmd reset-password @username:example.com
```

### 数据库操作
```bash
# 连接数据库
podman exec -it matrix-postgres psql -U synapse synapse

# 数据库备份
podman exec matrix-postgres pg_dump -U synapse synapse > backup.sql

# 数据库恢复
podman exec -i matrix-postgres psql -U synapse synapse < backup.sql

# 数据库优化
podman exec matrix-postgres psql -U synapse synapse -c "VACUUM ANALYZE;"
```

### 证书管理
```bash
# 手动更新证书
sudo /opt/matrix/scripts/update-certificates.sh

# 检查证书有效期
openssl x509 -in /opt/matrix/data/acme/fullchain.cer -noout -dates

# 测试证书
openssl s_client -connect localhost:8448 -servername matrix.example.com
```

## 🔍 故障排除

### 服务检查
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8448
sudo netstat -tlnp | grep :3478

# 检查磁盘空间
df -h /opt/matrix

# 检查内存使用
free -h

# 检查系统资源
htop
```

### 网络测试
```bash
# 测试Matrix API
curl -k https://localhost:8448/_matrix/client/versions

# 测试联邦
curl -k https://matrix.example.com:8448/_matrix/federation/v1/version

# 测试TURN服务器
nc -u -z localhost 3478
```

### 配置验证
```bash
# 验证Nginx配置
podman exec matrix-nginx nginx -t

# 验证Synapse配置
podman exec matrix-synapse python -m synapse.config -c /data/homeserver.yaml

# 检查证书链接
ls -la /opt/matrix/data/acme/
```

## 📊 监控命令

### 性能监控
```bash
# 查看容器资源使用
podman stats --no-stream

# 查看系统负载
uptime

# 查看磁盘I/O
iostat -x 1

# 查看网络连接
ss -tuln
```

### 日志分析
```bash
# 查看错误日志
grep -i error /opt/matrix/data/synapse/logs/homeserver.log

# 查看最近的登录
grep "Logged in" /opt/matrix/data/synapse/logs/homeserver.log | tail -10

# 查看联邦错误
grep -i federation /opt/matrix/data/synapse/logs/homeserver.log | grep -i error
```

## 🛠️ 配置文件位置

### 主要配置文件
```
/opt/matrix/config/
├── deployment.env          # 环境变量配置
├── homeserver.yaml         # Synapse主配置
├── log.config             # 日志配置
├── nginx.conf             # Nginx主配置
├── matrix.conf            # Nginx站点配置
├── turnserver.conf        # Coturn配置
└── postgres-init.sql      # 数据库初始化
```

### 数据目录
```
/opt/matrix/data/
├── postgres/              # PostgreSQL数据
├── redis/                 # Redis数据
├── synapse/               # Synapse数据
│   ├── media_store/       # 媒体文件
│   ├── logs/             # 日志文件
│   └── signing.key       # 签名密钥
├── nginx/logs/           # Nginx日志
├── coturn/logs/          # Coturn日志
└── acme/                 # SSL证书链接
```

## 🔄 备份和恢复

### 手动备份
```bash
# 完整备份
./scripts/backup-matrix.sh

# 仅备份数据库
podman exec matrix-postgres pg_dump -U synapse synapse > synapse_backup.sql

# 备份配置
tar -czf config_backup.tar.gz config/
```

### 恢复操作
```bash
# 停止服务
./scripts/manage-pod.sh stop

# 恢复数据库
podman exec -i matrix-postgres psql -U synapse synapse < synapse_backup.sql

# 恢复配置
tar -xzf config_backup.tar.gz

# 启动服务
./scripts/manage-pod.sh start
```

## 🚨 紧急操作

### 服务重置
```bash
# 完全重置（谨慎使用）
./scripts/manage-pod.sh stop
podman pod rm -f matrix-pod
./scripts/manage-pod.sh deploy
```

### 清理空间
```bash
# 清理容器镜像
podman image prune -a

# 清理旧日志
./scripts/cleanup-logs.sh

# 清理数据库
podman exec matrix-postgres psql -U synapse synapse -c "VACUUM FULL;"
```

## 📞 联系信息

- **项目文档**: `/opt/matrix/Matrix_Homeserver_Podman_手动部署指南_技术新手版.md`
- **配置模板**: `/opt/matrix/config/`
- **管理脚本**: `/opt/matrix/scripts/`
- **日志位置**: `/opt/matrix/data/*/logs/`

---

**提示**: 将此文件保存为书签，方便日常运维参考！
