# Matrix Homeserver Podman 快速参考卡

## 🚀 一键安装

```bash
# 下载并运行完整安装脚本
wget https://raw.githubusercontent.com/your-repo/matrix-homeserver/main/podman-complete-setup.sh
chmod +x podman-complete-setup.sh
./podman-complete-setup.sh
```

## ⚙️ 基础配置

### 必须修改的配置项

```bash
# 编辑配置文件
sudo nano /opt/matrix/config/deployment.env

# 必须修改的项目：
DOMAIN="your-domain.com"                    # 您的域名
CLOUDFLARE_API_TOKEN="your_token_here"      # Cloudflare API令牌
CLOUDFLARE_ZONE_ID="your_zone_id_here"      # Cloudflare Zone ID
CLOUDFLARE_EMAIL="<EMAIL>"   # 您的邮箱
```

### 获取Cloudflare配置

1. 登录 [Cloudflare控制台](https://dash.cloudflare.com/)
2. 进入 **我的个人资料** → **API令牌**
3. 创建令牌，权限：`Zone:Zone:Read` + `Zone:DNS:Edit`
4. 在域名管理页面右侧找到 **Zone ID**

## 🐳 服务管理

### 基础命令

```bash
# 切换到matrix用户
sudo su - matrix
cd /opt/matrix

# 查看服务状态
podman-compose ps

# 启动所有服务
podman-compose up -d

# 停止所有服务
podman-compose down

# 重启特定服务
podman-compose restart synapse

# 查看日志
podman-compose logs -f synapse
```

### 服务启动顺序

```bash
# 按顺序启动（推荐）
podman-compose up -d db redis     # 1. 数据库和缓存
sleep 30
podman-compose up -d synapse      # 2. Matrix核心服务
sleep 30
podman-compose up -d nginx coturn # 3. 代理和TURN服务
```

## 👤 用户管理

### 创建管理员用户

```bash
# 进入Synapse容器
podman exec -it matrix_synapse register_new_matrix_user \
    -c /data/homeserver.yaml \
    http://localhost:8008

# 按提示输入：
# 用户名: admin
# 密码: [强密码]
# 管理员: yes
```

### 创建普通用户

```bash
# 如果启用了注册，用户可以直接在客户端注册
# 如果禁用了注册，使用相同命令但选择 "管理员: no"
```

## 🔒 SSL证书管理

### 申请证书

```bash
# 安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>
source ~/.bashrc

# 设置Cloudflare API
export CF_Token="your_cloudflare_api_token"
export CF_Zone_ID="your_zone_id"

# 申请ECC证书
acme.sh --issue --dns dns_cf -d matrix.your-domain.com --keylength ec-256
```

### 配置证书链接

```bash
# 创建证书符号链接
DOMAIN="matrix.your-domain.com"
CERT_SOURCE="/root/.acme.sh/${DOMAIN}_ecc"

# Nginx证书
ln -sf "${CERT_SOURCE}/fullchain.cer" "/opt/matrix/data/nginx/certs/fullchain.cer"
ln -sf "${CERT_SOURCE}/${DOMAIN}.key" "/opt/matrix/data/nginx/certs/private.key"

# Coturn证书
ln -sf "${CERT_SOURCE}/fullchain.cer" "/opt/matrix/data/coturn/certs/fullchain.cer"
ln -sf "${CERT_SOURCE}/${DOMAIN}.key" "/opt/matrix/data/coturn/certs/private.key"

# 重启服务
podman-compose restart nginx coturn
```

## 🔧 故障排除

### 常用调试命令

```bash
# 检查容器状态
podman-compose ps

# 查看特定服务日志
podman-compose logs synapse
podman-compose logs db
podman-compose logs nginx

# 进入容器调试
podman exec -it matrix_synapse /bin/bash
podman exec -it matrix_postgres /bin/bash

# 检查端口监听
sudo netstat -tlnp | grep -E "(8448|3478|5349)"

# 测试API
curl -k https://matrix.your-domain.com:8448/_matrix/client/versions
```

### 常见问题解决

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 容器启动失败 | `Exited (1)` | 检查日志：`podman-compose logs [服务名]` |
| 数据库连接失败 | `psycopg2.OperationalError` | 重启数据库：`podman-compose restart db` |
| SSL证书错误 | `SSL: error` | 检查证书文件和符号链接 |
| 端口无法访问 | `Connection refused` | 检查防火墙和端口转发 |
| 内存不足 | `MemoryError` | 调整资源限制配置 |

## 📊 监控和维护

### 健康检查

```bash
# 运行健康检查脚本
./scripts/health_check.sh

# 检查系统资源
free -h                    # 内存使用
df -h /opt/matrix         # 磁盘使用
podman stats              # 容器资源使用
```

### 备份

```bash
# 手动备份
./scripts/backup.sh

# 查看备份文件
ls -la backup/

# 恢复数据库（示例）
podman exec -i matrix_postgres psql -U synapse synapse < backup/synapse_20231214_120000.sql
```

### 日志管理

```bash
# 清理旧日志
./scripts/cleanup_logs.sh

# 查看日志大小
du -sh data/*/logs/

# 实时查看日志
tail -f data/synapse/logs/homeserver.log
```

## 📱 客户端连接

### 连接信息

- **服务器地址**: `https://matrix.your-domain.com:8448`
- **用户名**: `@admin:your-domain.com`
- **密码**: 您设置的管理员密码

### 推荐客户端

- **Element Web**: https://app.element.io/
- **Element Desktop**: 桌面客户端
- **Element Mobile**: iOS/Android应用
- **FluffyChat**: 轻量级移动客户端
- **Nheko**: 桌面客户端

## 🔄 更新升级

### 更新服务

```bash
# 运行更新脚本
./scripts/update.sh

# 或手动更新
podman-compose pull       # 拉取最新镜像
podman-compose down       # 停止服务
podman-compose up -d      # 启动服务
```

### 配置更新

```bash
# 备份当前配置
cp config/deployment.env config/deployment.env.backup

# 编辑配置
nano config/deployment.env

# 重启服务应用配置
podman-compose down
podman-compose up -d
```

## 📞 获取帮助

### 日志位置

- **Synapse**: `/opt/matrix/data/synapse/logs/`
- **Nginx**: `/opt/matrix/data/nginx/logs/`
- **Coturn**: `/opt/matrix/data/coturn/logs/`
- **系统日志**: `/var/log/matrix/`

### 配置文件位置

- **主配置**: `/opt/matrix/config/deployment.env`
- **Compose**: `/opt/matrix/docker-compose.yml`
- **Synapse**: `/opt/matrix/config/homeserver.yaml`
- **Nginx**: `/opt/matrix/data/nginx/conf/`
- **Coturn**: `/opt/matrix/data/coturn/conf/`

### 有用的链接

- [Matrix官方文档](https://matrix.org/docs/)
- [Synapse管理指南](https://matrix-org.github.io/synapse/latest/)
- [Podman官方文档](https://docs.podman.io/)
- [Element客户端](https://element.io/)

---

## 🆘 紧急恢复

### 完全重置

```bash
# 停止所有服务
podman-compose down

# 清理所有容器和镜像（谨慎使用）
podman system prune -a

# 重新部署
podman-compose up -d
```

### 数据恢复

```bash
# 从备份恢复数据库
podman exec -i matrix_postgres psql -U synapse synapse < backup/latest_backup.sql

# 恢复配置文件
tar -xzf backup/config_backup.tar.gz -C /opt/matrix/
```

---

**💡 提示**: 将此参考卡保存为书签，方便随时查阅！
