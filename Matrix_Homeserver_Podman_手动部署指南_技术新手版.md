# Matrix Homeserver Podman手动部署指南 - 技术新手版

## 📋 文档概述

本指南专门为没有Docker/容器化经验的技术初学者设计，提供完整的Matrix Homeserver Podman部署方案。所有命令都可以直接复制粘贴执行，无需手动编辑。

### 🎯 目标用户
- 技术初学者，无Docker/Podman经验
- 希望自建Matrix服务器的个人用户
- 需要完整独立部署文档的开发者

### 📦 部署架构
本指南使用Podman Pod方案，不依赖podman-compose，提供更好的容器管理和资源控制。

---

## 🖥️ 系统要求

### 硬件要求
- **CPU**: 2核心以上（推荐4核心）
- **内存**: 4GB以上（推荐8GB）
- **存储**: 50GB以上可用空间（推荐100GB）
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Debian 12 (Bookworm)
- **架构**: x86_64
- **权限**: root访问权限

### 网络要求
- **域名**: 已注册的域名（如 example.com）
- **DNS**: 可配置DNS记录的权限
- **端口**: 以下端口需要在路由器上配置端口转发
  - 8448 (TCP) - Matrix联邦通信
  - 3478 (TCP/UDP) - STUN服务
  - 5349 (TCP) - TURNS服务
  - 49152-65535 (UDP) - TURN媒体转发

---

## 🚀 第一步：环境准备

### 1.1 系统更新

首先更新系统到最新状态：

```bash
# 更新软件包列表
root@server:~# apt update

# 升级所有软件包
root@server:~# apt upgrade -y

# 安装基础工具
root@server:~# apt install -y curl wget git vim htop tree unzip
```

**预期输出**：系统显示软件包更新进度，完成后返回命令提示符。

### 1.2 创建专用用户

为Matrix服务创建专用用户，提高安全性：

```bash
# 创建matrix用户
root@server:~# useradd -m -s /bin/bash matrix

# 将matrix用户添加到sudo组
root@server:~# usermod -aG sudo matrix

# 设置matrix用户密码
root@server:~# passwd matrix
```

**预期交互**：系统提示输入新密码，输入两次相同密码确认。

### 1.3 安装Podman

安装Podman容器运行时：

```bash
# 安装Podman及相关工具
root@server:~# apt install -y podman buildah skopeo

# 验证Podman安装
root@server:~# podman --version
```

**预期输出**：显示Podman版本信息，如 "podman version 4.3.1"。

### 1.4 配置Podman无根模式

为matrix用户配置无根容器支持：

```bash
# 配置用户命名空间
root@server:~# echo 'matrix:100000:65536' >> /etc/subuid
root@server:~# echo 'matrix:100000:65536' >> /etc/subgid

# 启用用户lingering（允许用户服务在登出后继续运行）
root@server:~# loginctl enable-linger matrix
```

### 1.5 创建项目目录结构

切换到matrix用户并创建项目目录：

```bash
# 切换到matrix用户
root@server:~# su - matrix

# 创建项目根目录
matrix@server:~$ sudo mkdir -p /opt/matrix

# 设置目录所有权
matrix@server:~$ sudo chown -R matrix:matrix /opt/matrix

# 进入项目目录
matrix@server:~$ cd /opt/matrix

# 创建完整目录结构
matrix@server:/opt/matrix$ mkdir -p {config,data/{postgres,redis,synapse,nginx,coturn,acme,logs},scripts,backup}
```

---

## 🔧 第二步：Python环境配置

### 2.1 安装Python虚拟环境

Matrix项目需要Python环境来运行管理脚本：

```bash
# 安装Python和虚拟环境工具
matrix@server:/opt/matrix$ sudo apt install -y python3 python3-venv python3-pip

# 创建Python虚拟环境
matrix@server:/opt/matrix$ python3 -m venv venv

# 激活虚拟环境
matrix@server:/opt/matrix$ source venv/bin/activate

# 升级pip到最新版本
(venv) matrix@server:/opt/matrix$ pip install --upgrade pip
```

**预期输出**：命令提示符前出现 "(venv)" 标识，表示虚拟环境已激活。

### 2.2 安装Python依赖

安装Matrix项目所需的Python库：

```bash
# 安装RouterOS API客户端（用于动态IP监控）
(venv) matrix@server:/opt/matrix$ pip install librouteros

# 安装其他必要库
(venv) matrix@server:/opt/matrix$ pip install requests pyyaml jinja2
```

---

## 📝 第三步：配置文件准备

### 3.1 创建环境配置文件

创建主要的环境配置文件：

```bash
# 创建环境配置文件
(venv) matrix@server:/opt/matrix$ cat > config/deployment.env << 'EOF'
# ================================================================
# Matrix Homeserver Podman部署配置
# ================================================================

# 基础域名配置
DOMAIN="example.com"                    # 替换为您的实际域名
SUBDOMAIN_MATRIX="matrix"               # Matrix服务子域名
MATRIX_DOMAIN="matrix.example.com"     # 完整Matrix域名

# 网络端口配置
HTTPS_PORT="8448"                       # Matrix联邦端口
TURN_PORT="3478"                        # STUN/TURN端口
TURNS_PORT="5349"                       # TURNS加密端口
COTURN_MIN_PORT="49152"                 # TURN UDP端口范围开始
COTURN_MAX_PORT="65535"                 # TURN UDP端口范围结束

# 部署路径配置
DEPLOY_DIR="/opt/matrix"                # 主部署目录
DATA_DIR="/opt/matrix/data"             # 数据目录
CONFIG_DIR="/opt/matrix/config"         # 配置目录
LOG_DIR="/opt/matrix/data/logs"         # 日志目录

# 数据库配置
DB_NAME="synapse"                       # 数据库名称
DB_USER="synapse"                       # 数据库用户名
DB_PASSWORD="CHANGE_ME_SECURE_PASSWORD" # 数据库密码（必须修改）
DB_HOST="127.0.0.1"                     # 数据库主机
DB_PORT="5432"                          # 数据库端口

# Redis配置
REDIS_HOST="127.0.0.1"                  # Redis主机
REDIS_PORT="6379"                       # Redis端口

# 证书配置
ACME_SOURCE_DIR="/root/.acme.sh"        # acme.sh证书源目录
ACME_DEPLOY_DIR="/opt/matrix/data/acme" # 证书部署目录
CERT_TYPE="ECC"                         # 证书类型 (ECC/RSA)

# Synapse配置
SYNAPSE_SERVER_NAME="example.com"       # 服务器名称（与DOMAIN相同）
ENABLE_REGISTRATION="false"             # 是否允许注册
MAX_UPLOAD_SIZE="50M"                   # 最大上传文件大小

# Coturn配置
COTURN_SHARED_SECRET="CHANGE_ME_COTURN_SECRET"  # Coturn共享密钥（必须修改）

# 安全配置
POSTGRES_MEMORY_LIMIT="1g"              # PostgreSQL内存限制
REDIS_MEMORY_LIMIT="512m"               # Redis内存限制
SYNAPSE_MEMORY_LIMIT="2g"               # Synapse内存限制
COTURN_MEMORY_LIMIT="512m"              # Coturn内存限制

# 动态IP监控配置（可选）
ROUTEROS_HOST=""                        # RouterOS设备IP
ROUTEROS_USER=""                        # RouterOS用户名
ROUTEROS_PASSWORD=""                    # RouterOS密码
ROUTEROS_WAN_INTERFACE="WAN"            # WAN接口名称
EOF
```

**重要提示**：请务必修改以下配置项：
- `DOMAIN`: 替换为您的实际域名
- `DB_PASSWORD`: 设置强密码
- `COTURN_SHARED_SECRET`: 设置强密钥

### 3.2 创建Podman Pod配置脚本

创建Podman Pod管理脚本：

```bash
# 创建Pod管理脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/manage-pod.sh << 'EOF'
#!/bin/bash
# Matrix Homeserver Podman Pod管理脚本

set -euo pipefail

# 加载配置
source /opt/matrix/config/deployment.env

# Pod名称
POD_NAME="matrix-pod"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }

# 创建Pod
create_pod() {
    log_info "创建Matrix Pod..."

    # 删除已存在的Pod（如果有）
    podman pod exists $POD_NAME && podman pod rm -f $POD_NAME

    # 创建新Pod，配置端口映射
    podman pod create \
        --name $POD_NAME \
        --publish 127.0.0.1:${HTTPS_PORT}:${HTTPS_PORT} \
        --publish ${TURN_PORT}:${TURN_PORT}/tcp \
        --publish ${TURN_PORT}:${TURN_PORT}/udp \
        --publish ${TURNS_PORT}:${TURNS_PORT}/tcp \
        --publish ${COTURN_MIN_PORT}-${COTURN_MAX_PORT}:${COTURN_MIN_PORT}-${COTURN_MAX_PORT}/udp

    log_info "Pod创建完成"
}

# 启动PostgreSQL容器
start_postgres() {
    log_info "启动PostgreSQL容器..."

    podman run -d \
        --pod $POD_NAME \
        --name matrix-postgres \
        --restart unless-stopped \
        -e POSTGRES_DB=$DB_NAME \
        -e POSTGRES_USER=$DB_USER \
        -e POSTGRES_PASSWORD=$DB_PASSWORD \
        -e POSTGRES_INITDB_ARGS="--encoding=UTF-8 --lc-collate=C --lc-ctype=C" \
        -v $DATA_DIR/postgres:/var/lib/postgresql/data:Z \
        --memory=$POSTGRES_MEMORY_LIMIT \
        postgres:15-alpine

    log_info "PostgreSQL容器启动完成"
}

# 启动Redis容器
start_redis() {
    log_info "启动Redis容器..."

    podman run -d \
        --pod $POD_NAME \
        --name matrix-redis \
        --restart unless-stopped \
        -v $DATA_DIR/redis:/data:Z \
        --memory=$REDIS_MEMORY_LIMIT \
        redis:7-alpine redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

    log_info "Redis容器启动完成"
}

# 启动Synapse容器
start_synapse() {
    log_info "启动Synapse容器..."

    podman run -d \
        --pod $POD_NAME \
        --name matrix-synapse \
        --restart unless-stopped \
        -e SYNAPSE_SERVER_NAME=$SYNAPSE_SERVER_NAME \
        -e SYNAPSE_REPORT_STATS="no" \
        -e SYNAPSE_CONFIG_PATH=/data/homeserver.yaml \
        -v $DATA_DIR/synapse:/data:Z \
        -v $CONFIG_DIR/homeserver.yaml:/data/homeserver.yaml:ro,Z \
        -v $CONFIG_DIR/log.config:/data/log.config:ro,Z \
        --memory=$SYNAPSE_MEMORY_LIMIT \
        matrixdotorg/synapse:latest

    log_info "Synapse容器启动完成"
}

# 启动Nginx容器
start_nginx() {
    log_info "启动Nginx容器..."

    podman run -d \
        --pod $POD_NAME \
        --name matrix-nginx \
        --restart unless-stopped \
        -v $CONFIG_DIR/nginx.conf:/etc/nginx/nginx.conf:ro,Z \
        -v $CONFIG_DIR/matrix.conf:/etc/nginx/conf.d/matrix.conf:ro,Z \
        -v $DATA_DIR/nginx/logs:/var/log/nginx:Z \
        -v $DATA_DIR/acme:/etc/nginx/certs:ro,Z \
        -v $DATA_DIR/synapse/media:/var/www/matrix/media:ro,Z \
        nginx:alpine

    log_info "Nginx容器启动完成"
}

# 启动Coturn容器
start_coturn() {
    log_info "启动Coturn容器..."

    podman run -d \
        --pod $POD_NAME \
        --name matrix-coturn \
        --restart unless-stopped \
        -v $CONFIG_DIR/turnserver.conf:/etc/coturn/turnserver.conf:ro,Z \
        -v $DATA_DIR/coturn/logs:/var/log/coturn:Z \
        -v $DATA_DIR/acme:/etc/coturn/certs:ro,Z \
        --memory=$COTURN_MEMORY_LIMIT \
        coturn/coturn:latest

    log_info "Coturn容器启动完成"
}

# 停止所有服务
stop_services() {
    log_info "停止Matrix服务..."
    podman pod stop $POD_NAME
    log_info "服务停止完成"
}

# 启动所有服务
start_services() {
    log_info "启动Matrix服务..."
    podman pod start $POD_NAME
    log_info "服务启动完成"
}

# 重启所有服务
restart_services() {
    log_info "重启Matrix服务..."
    podman pod restart $POD_NAME
    log_info "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "Matrix服务状态："
    podman pod ps
    echo
    podman ps --pod
}

# 查看服务日志
show_logs() {
    local service=${1:-}
    if [[ -n $service ]]; then
        podman logs -f matrix-$service
    else
        log_info "可用的服务日志："
        echo "  postgres, redis, synapse, nginx, coturn"
        echo "使用方法: $0 logs <service_name>"
    fi
}

# 完整部署
deploy() {
    log_info "开始完整部署..."
    create_pod
    start_postgres
    sleep 10  # 等待PostgreSQL启动
    start_redis
    sleep 5   # 等待Redis启动
    start_synapse
    sleep 10  # 等待Synapse启动
    start_nginx
    start_coturn
    log_info "部署完成！"
    show_status
}

# 主函数
main() {
    case "${1:-}" in
        "create-pod")
            create_pod
            ;;
        "deploy")
            deploy
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "${2:-}"
            ;;
        *)
            echo "使用方法: $0 {create-pod|deploy|start|stop|restart|status|logs [service]}"
            echo
            echo "命令说明："
            echo "  create-pod  - 创建Pod"
            echo "  deploy      - 完整部署所有服务"
            echo "  start       - 启动所有服务"
            echo "  stop        - 停止所有服务"
            echo "  restart     - 重启所有服务"
            echo "  status      - 查看服务状态"
            echo "  logs        - 查看服务日志"
            exit 1
            ;;
    esac
}

main "$@"
EOF

# 设置脚本执行权限
(venv) matrix@server:/opt/matrix$ chmod +x scripts/manage-pod.sh
```

### 3.3 创建Synapse配置文件

创建Matrix Synapse服务器的主配置文件：

```bash
# 创建Synapse配置文件
(venv) matrix@server:/opt/matrix$ cat > config/homeserver.yaml << 'EOF'
# Matrix Synapse Homeserver配置文件
# 此配置专为Podman部署优化

# ================================================================
# 服务器基础配置
# ================================================================

# 服务器名称（必须与您的域名一致）
server_name: "example.com"

# 进程ID文件位置
pid_file: /data/homeserver.pid

# Web客户端位置（使用官方Element客户端）
web_client_location: https://app.element.io/

# 公共基础URL（用于生成链接）
public_baseurl: https://matrix.example.com:8448/

# ================================================================
# 网络监听配置
# ================================================================

listeners:
  # HTTP监听器（内部通信）
  - port: 8008
    tls: false
    type: http
    x_forwarded: true  # 信任反向代理的X-Forwarded-* 头部
    bind_addresses: ['0.0.0.0']

    resources:
      # 客户端API和联邦API
      - names: [client, federation]
        compress: false  # 由Nginx处理压缩

# ================================================================
# 数据库配置
# ================================================================

database:
  name: psycopg2  # PostgreSQL驱动
  args:
    user: synapse
    password: CHANGE_ME_SECURE_PASSWORD  # 与deployment.env中的DB_PASSWORD保持一致
    database: synapse
    host: 127.0.0.1  # Pod内部通信
    port: 5432
    cp_min: 5      # 最小连接池大小
    cp_max: 10     # 最大连接池大小

# ================================================================
# Redis缓存配置
# ================================================================

redis:
  enabled: true
  host: 127.0.0.1  # Pod内部通信
  port: 6379

# ================================================================
# 日志配置
# ================================================================

log_config: "/data/log.config"

# ================================================================
# 媒体存储配置
# ================================================================

media_store_path: "/data/media_store"

# 上传限制
max_upload_size: 50M

# 媒体保留策略
media_retention:
  # 本地媒体保留时间（天）
  local_media_lifetime: 365d
  # 远程媒体保留时间（天）
  remote_media_lifetime: 90d

# ================================================================
# 用户注册配置
# ================================================================

# 是否允许新用户注册
enable_registration: false

# 是否允许访客访问
allow_guest_access: false

# 注册共享密钥（如果启用注册）
# registration_shared_secret: "CHANGE_ME_REGISTRATION_SECRET"

# ================================================================
# 联邦配置
# ================================================================

# 是否允许与其他Matrix服务器联邦
federation_domain_whitelist: []

# 联邦发送者配置
federation_sender_instances:
  - federation_sender1

# ================================================================
# TURN服务器配置
# ================================================================

turn_uris:
  - "turn:matrix.example.com:3478?transport=udp"
  - "turn:matrix.example.com:3478?transport=tcp"
  - "turns:matrix.example.com:5349?transport=tcp"

turn_shared_secret: "CHANGE_ME_COTURN_SECRET"  # 与deployment.env中的COTURN_SHARED_SECRET保持一致

turn_user_lifetime: 86400000  # 24小时

# ================================================================
# 安全配置
# ================================================================

# 密码策略
password_config:
  enabled: true
  policy:
    minimum_length: 8
    require_digit: true
    require_symbol: true
    require_lowercase: true
    require_uppercase: true

# 速率限制
rc_message:
  per_second: 0.2
  burst_count: 10

rc_registration:
  per_second: 0.17
  burst_count: 3

rc_login:
  address:
    per_second: 0.17
    burst_count: 3
  account:
    per_second: 0.17
    burst_count: 3
  failed_attempts:
    per_second: 0.17
    burst_count: 3

# ================================================================
# 性能配置
# ================================================================

# 事件缓存大小
event_cache_size: 10K

# 全局缓存配置
caches:
  global_factor: 0.5
  per_cache_factors:
    get_users_who_share_room_with_user: 2.0

# ================================================================
# 其他配置
# ================================================================

# 报告统计信息（建议关闭）
report_stats: false

# 抑制关键配置警告
suppress_key_server_warning: true

# 信任身份服务器
trusted_key_servers:
  - server_name: "matrix.org"

# 签名密钥文件
signing_key_path: "/data/signing.key"

# 旧签名密钥
old_signing_keys: {}

# 密钥刷新间隔
key_refresh_interval: "1d"
EOF
```

**重要提示**：请务必修改配置文件中的以下项目：
- `server_name`: 替换为您的实际域名
- `public_baseurl`: 替换为您的实际Matrix域名
- `password`: 与deployment.env中的DB_PASSWORD保持一致
- `turn_shared_secret`: 与deployment.env中的COTURN_SHARED_SECRET保持一致

### 3.4 创建日志配置文件

创建Synapse的日志配置：

```bash
# 创建日志配置文件
(venv) matrix@server:/opt/matrix$ cat > config/log.config << 'EOF'
# Synapse日志配置文件

version: 1

formatters:
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'

handlers:
  file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: precise
    filename: /data/logs/homeserver.log
    when: midnight
    backupCount: 3  # 保留3天的日志
    encoding: utf8

  console:
    class: logging.StreamHandler
    formatter: precise

loggers:
  synapse.storage.SQL:
    # 数据库查询日志级别（生产环境建议设为WARNING）
    level: WARNING

  synapse.access.http.8008:
    # HTTP访问日志级别
    level: INFO

root:
  level: INFO
  handlers: [file, console]

disable_existing_loggers: false
EOF
```

### 3.5 创建Nginx配置文件

创建Nginx反向代理配置：

```bash
# 创建Nginx主配置文件
(venv) matrix@server:/opt/matrix$ cat > config/nginx.conf << 'EOF'
# Nginx主配置文件 - Matrix Homeserver专用

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
EOF

# 创建Matrix站点配置文件
(venv) matrix@server:/opt/matrix$ cat > config/matrix.conf << 'EOF'
# Matrix Homeserver Nginx站点配置

server {
    listen 8448 ssl http2;
    server_name matrix.example.com;  # 替换为您的实际域名

    # SSL证书配置
    ssl_certificate /etc/nginx/certs/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/private.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # 客户端上传大小限制
    client_max_body_size 50M;

    # Matrix客户端API
    location ~ ^(/_matrix|/_synapse/client) {
        proxy_pass http://127.0.0.1:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Matrix联邦API
    location ~ ^/_matrix/federation {
        proxy_pass http://127.0.0.1:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 媒体文件直接服务（可选优化）
    location ~ ^/_matrix/media {
        proxy_pass http://127.0.0.1:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 媒体文件缓存
        proxy_cache_valid 200 1d;
        proxy_cache_use_stale error timeout invalid_header updating;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name matrix.example.com;  # 替换为您的实际域名
    return 301 https://$server_name:8448$request_uri;
}
EOF
```

**重要提示**：请务必修改Nginx配置中的域名：
- 将 `matrix.example.com` 替换为您的实际Matrix域名

### 3.6 创建Coturn配置文件

创建TURN/STUN服务器配置：

```bash
# 创建Coturn配置文件
(venv) matrix@server:/opt/matrix$ cat > config/turnserver.conf << 'EOF'
# Coturn TURN/STUN服务器配置文件

# ================================================================
# 基础网络配置
# ================================================================

# 监听端口配置
listening-port=3478          # STUN/TURN端口
tls-listening-port=5349      # TURNS加密端口
listening-ip=0.0.0.0         # 监听所有接口

# 外部IP配置（需要根据实际情况修改）
external-ip=YOUR_PUBLIC_IP   # 替换为您的公网IP

# 中继IP配置
relay-ip=0.0.0.0

# 端口范围配置
min-port=49152               # UDP端口范围开始
max-port=65535               # UDP端口范围结束

# ================================================================
# 域名和认证配置
# ================================================================

# 域名配置
realm=example.com            # 替换为您的实际域名
server-name=matrix.example.com  # 替换为您的Matrix域名

# 认证配置
use-auth-secret              # 使用共享密钥认证
static-auth-secret=CHANGE_ME_COTURN_SECRET  # 与deployment.env保持一致

# ================================================================
# 证书配置
# ================================================================

# SSL证书路径（使用符号链接）
cert=/etc/coturn/certs/fullchain.cer
pkey=/etc/coturn/certs/private.key

# ================================================================
# 协议和安全配置
# ================================================================

# 启用指纹验证
fingerprint

# 启用长期凭证机制
lt-cred-mech

# 禁用不安全的TLS版本
no-tlsv1
no-tlsv1_1

# ================================================================
# 日志配置
# ================================================================

# 禁用标准输出日志
no-stdout-log

# 日志文件路径
log-file=/var/log/coturn/coturn.log

# PID文件路径
pidfile=/var/run/coturn/coturn.pid

# 详细日志（生产环境可关闭）
verbose

# ================================================================
# 性能配置
# ================================================================

# 总配额限制
total-quota=100

# 带宽容量（0表示无限制）
bps-capacity=0

# 过期nonce时间（秒）
stale-nonce=600

# ================================================================
# 网络限制配置
# ================================================================

# 禁用多播对等
no-multicast-peers

# 禁用命令行接口
no-cli

# 禁用环回对等
no-loopback-peers

# 禁用TCP中继（仅使用UDP）
no-tcp-relay

# ================================================================
# 安全限制配置
# ================================================================

# 拒绝的对等IP范围（内网地址）
denied-peer-ip=10.0.0.0-**************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************
denied-peer-ip=*********-***************

# 允许的源地址（所有公网地址）
allowed-peer-ip=0.0.0.0-***************

# ================================================================
# 其他配置
# ================================================================

# 数据库配置（使用内存数据库）
userdb=/var/lib/coturn/turndb

# 仅STUN模式（如果只需要STUN功能）
# stun-only

# 移动性支持
mobility

# 软件标识
prod
EOF
```

**重要提示**：请务必修改Coturn配置中的以下项目：
- `external-ip`: 替换为您的实际公网IP
- `realm`: 替换为您的实际域名
- `server-name`: 替换为您的实际Matrix域名
- `static-auth-secret`: 与deployment.env中的COTURN_SHARED_SECRET保持一致

---

## 🔐 第四步：证书管理配置

### 4.1 安装acme.sh证书工具

安装自动化SSL证书管理工具：

```bash
# 切换到root用户安装acme.sh
(venv) matrix@server:/opt/matrix$ exit
matrix@server:~$ sudo su -

# 下载并安装acme.sh
root@server:~# curl https://get.acme.sh | sh -s email=<EMAIL>

# 重新加载环境变量
root@server:~# source ~/.bashrc

# 验证安装
root@server:~# ~/.acme.sh/acme.sh --version
```

**预期输出**：显示acme.sh版本信息。

### 4.2 配置Cloudflare API（如果使用Cloudflare）

如果您的域名使用Cloudflare管理，需要配置API访问：

```bash
# 设置Cloudflare API凭据
root@server:~# export CF_Token="your_cloudflare_api_token"
root@server:~# export CF_Zone_ID="your_cloudflare_zone_id"

# 申请证书（ECC类型）
root@server:~# ~/.acme.sh/acme.sh --issue --dns dns_cf -d example.com -d matrix.example.com --keylength ec-256

# 申请证书（RSA类型，备用）
root@server:~# ~/.acme.sh/acme.sh --issue --dns dns_cf -d example.com -d matrix.example.com --keylength 2048
```

### 4.3 创建证书符号链接

创建证书符号链接到部署目录：

```bash
# 创建证书目录
root@server:~# mkdir -p /opt/matrix/data/acme

# 创建ECC证书符号链接
root@server:~# ln -sf /root/.acme.sh/example.com_ecc/fullchain.cer /opt/matrix/data/acme/fullchain.cer
root@server:~# ln -sf /root/.acme.sh/example.com_ecc/example.com.key /opt/matrix/data/acme/private.key

# 设置证书文件权限
root@server:~# chown -R matrix:matrix /opt/matrix/data/acme
root@server:~# chmod 644 /opt/matrix/data/acme/fullchain.cer
root@server:~# chmod 600 /opt/matrix/data/acme/private.key
```

### 4.4 创建证书更新脚本

创建自动证书更新脚本：

```bash
# 创建证书更新脚本
root@server:~# cat > /opt/matrix/scripts/update-certificates.sh << 'EOF'
#!/bin/bash
# Matrix证书自动更新脚本

set -euo pipefail

# 配置变量
DOMAIN="example.com"  # 替换为您的实际域名
ACME_DIR="/root/.acme.sh"
DEPLOY_DIR="/opt/matrix/data/acme"
MATRIX_USER="matrix"

# 日志函数
log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*"; }

# 更新证书
update_certificates() {
    log_info "开始更新证书..."

    # 更新ECC证书
    if $ACME_DIR/acme.sh --renew -d $DOMAIN --ecc; then
        log_info "ECC证书更新成功"

        # 更新符号链接
        ln -sf $ACME_DIR/${DOMAIN}_ecc/fullchain.cer $DEPLOY_DIR/fullchain.cer
        ln -sf $ACME_DIR/${DOMAIN}_ecc/${DOMAIN}.key $DEPLOY_DIR/private.key

        # 设置权限
        chown -R $MATRIX_USER:$MATRIX_USER $DEPLOY_DIR
        chmod 644 $DEPLOY_DIR/fullchain.cer
        chmod 600 $DEPLOY_DIR/private.key

        log_info "证书链接更新完成"

        # 重启相关服务
        if systemctl --user -M $MATRIX_USER@ is-active --quiet matrix-pod; then
            systemctl --user -M $MATRIX_USER@ restart matrix-pod
            log_info "Matrix服务重启完成"
        fi

    else
        log_error "证书更新失败"
        exit 1
    fi
}

# 主函数
main() {
    update_certificates
    log_info "证书更新流程完成"
}

main "$@"
EOF

# 设置脚本权限
root@server:~# chmod +x /opt/matrix/scripts/update-certificates.sh

# 添加到crontab（每天凌晨2点检查更新）
root@server:~# (crontab -l 2>/dev/null; echo "0 2 * * * /opt/matrix/scripts/update-certificates.sh >> /var/log/matrix-cert-update.log 2>&1") | crontab -
```

---

## 🚀 第五步：服务部署

### 5.1 修改配置文件中的占位符

在部署之前，需要修改配置文件中的占位符为实际值：

```bash
# 切换回matrix用户
root@server:~# su - matrix
matrix@server:~$ cd /opt/matrix

# 激活Python虚拟环境
matrix@server:/opt/matrix$ source venv/bin/activate

# 创建配置更新脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/update-config.sh << 'EOF'
#!/bin/bash
# 配置文件占位符替换脚本

set -euo pipefail

# 加载环境变量
source /opt/matrix/config/deployment.env

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }

# 更新Synapse配置
update_synapse_config() {
    log_info "更新Synapse配置文件..."

    sed -i "s/example\.com/$DOMAIN/g" config/homeserver.yaml
    sed -i "s/matrix\.example\.com/$MATRIX_DOMAIN/g" config/homeserver.yaml
    sed -i "s/CHANGE_ME_SECURE_PASSWORD/$DB_PASSWORD/g" config/homeserver.yaml
    sed -i "s/CHANGE_ME_COTURN_SECRET/$COTURN_SHARED_SECRET/g" config/homeserver.yaml

    log_info "Synapse配置更新完成"
}

# 更新Nginx配置
update_nginx_config() {
    log_info "更新Nginx配置文件..."

    sed -i "s/matrix\.example\.com/$MATRIX_DOMAIN/g" config/matrix.conf

    log_info "Nginx配置更新完成"
}

# 更新Coturn配置
update_coturn_config() {
    log_info "更新Coturn配置文件..."

    # 获取公网IP（自动检测）
    PUBLIC_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "AUTO_DETECT_FAILED")

    if [[ "$PUBLIC_IP" == "AUTO_DETECT_FAILED" ]]; then
        log_warn "无法自动检测公网IP，请手动修改config/turnserver.conf中的external-ip"
    else
        log_info "检测到公网IP: $PUBLIC_IP"
        sed -i "s/YOUR_PUBLIC_IP/$PUBLIC_IP/g" config/turnserver.conf
    fi

    sed -i "s/example\.com/$DOMAIN/g" config/turnserver.conf
    sed -i "s/matrix\.example\.com/$MATRIX_DOMAIN/g" config/turnserver.conf
    sed -i "s/CHANGE_ME_COTURN_SECRET/$COTURN_SHARED_SECRET/g" config/turnserver.conf

    log_info "Coturn配置更新完成"
}

# 主函数
main() {
    log_info "开始更新配置文件..."
    update_synapse_config
    update_nginx_config
    update_coturn_config
    log_info "所有配置文件更新完成"

    log_warn "请检查以下配置是否正确："
    echo "  - 域名设置"
    echo "  - 数据库密码"
    echo "  - Coturn共享密钥"
    echo "  - 公网IP地址"
}

main "$@"
EOF

# 设置脚本权限并执行
(venv) matrix@server:/opt/matrix$ chmod +x scripts/update-config.sh
(venv) matrix@server:/opt/matrix$ ./scripts/update-config.sh
```

**重要提示**：执行脚本后，请检查配置文件是否正确更新。如果自动检测的公网IP不正确，请手动修改 `config/turnserver.conf` 文件。

### 5.2 初始化数据库

在启动服务之前，需要初始化PostgreSQL数据库：

```bash
# 创建数据库初始化脚本
(venv) matrix@server:/opt/matrix$ cat > config/postgres-init.sql << 'EOF'
-- Matrix Homeserver PostgreSQL初始化脚本

-- 创建Synapse数据库（如果不存在）
-- 注意：这个脚本在容器首次启动时自动执行

-- 设置数据库编码和排序规则
-- 这些设置确保Matrix能够正确处理Unicode字符
ALTER DATABASE synapse SET default_text_search_config = 'pg_catalog.english';

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 优化PostgreSQL配置（可选）
-- 这些设置可以提高Matrix的性能
-- ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
-- ALTER SYSTEM SET max_connections = 200;
-- ALTER SYSTEM SET shared_buffers = '256MB';
-- ALTER SYSTEM SET effective_cache_size = '1GB';
-- ALTER SYSTEM SET maintenance_work_mem = '64MB';
-- ALTER SYSTEM SET checkpoint_completion_target = 0.9;
-- ALTER SYSTEM SET wal_buffers = '16MB';
-- ALTER SYSTEM SET default_statistics_target = 100;

-- 重新加载配置
-- SELECT pg_reload_conf();
EOF
```

### 5.3 创建必要的目录和权限

确保所有数据目录都已创建并设置正确的权限：

```bash
# 创建所有必要的数据目录
(venv) matrix@server:/opt/matrix$ mkdir -p data/{postgres,redis,synapse/{media_store,logs},nginx/logs,coturn/logs}

# 创建Synapse签名密钥目录
(venv) matrix@server:/opt/matrix$ mkdir -p data/synapse/keys

# 设置目录权限
(venv) matrix@server:/opt/matrix$ chmod -R 755 data/
(venv) matrix@server:/opt/matrix$ chmod -R 777 data/postgres  # PostgreSQL需要特殊权限
(venv) matrix@server:/opt/matrix$ chmod -R 777 data/redis     # Redis需要特殊权限
```

### 5.4 部署Matrix服务

现在开始部署所有Matrix服务：

```bash
# 使用管理脚本进行完整部署
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh deploy
```

**预期输出**：脚本会依次创建Pod并启动所有容器服务。

### 5.5 验证服务状态

检查所有服务是否正常运行：

```bash
# 查看Pod和容器状态
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh status

# 查看具体服务日志
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh logs postgres
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh logs redis
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh logs synapse
```

**预期输出**：所有容器状态应显示为 "Up" 或 "Running"。

### 5.6 生成Synapse签名密钥

如果是首次部署，需要生成Synapse签名密钥：

```bash
# 生成签名密钥（如果不存在）
(venv) matrix@server:/opt/matrix$ podman exec matrix-synapse generate_signing_key.py -o /data/signing.key

# 验证密钥文件
(venv) matrix@server:/opt/matrix$ ls -la data/synapse/signing.key
```

### 5.7 创建管理员用户

创建Matrix服务器的第一个管理员用户：

```bash
# 进入Synapse容器创建管理员用户
(venv) matrix@server:/opt/matrix$ podman exec -it matrix-synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008
```

**预期交互过程**：
1. 输入用户名（如：admin）
2. 输入密码（建议使用强密码）
3. 确认密码
4. 选择是否为管理员（输入 yes）

---

## 🔍 第六步：配置验证和测试

### 6.1 基础连通性测试

验证各个服务的基础连通性：

```bash
# 测试PostgreSQL连接
(venv) matrix@server:/opt/matrix$ podman exec matrix-postgres pg_isready -U synapse

# 测试Redis连接
(venv) matrix@server:/opt/matrix$ podman exec matrix-redis redis-cli ping

# 测试Synapse API
(venv) matrix@server:/opt/matrix$ curl -k https://localhost:8448/_matrix/client/versions

# 测试Nginx配置
(venv) matrix@server:/opt/matrix$ podman exec matrix-nginx nginx -t
```

**预期输出**：
- PostgreSQL: "accepting connections"
- Redis: "PONG"
- Synapse: JSON格式的版本信息
- Nginx: "syntax is ok" 和 "test is successful"

### 6.2 Matrix联邦测试

使用在线工具测试Matrix联邦功能：

```bash
# 创建联邦测试脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/test-federation.sh << 'EOF'
#!/bin/bash
# Matrix联邦测试脚本

set -euo pipefail

# 加载配置
source /opt/matrix/config/deployment.env

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }

# 测试Matrix服务器发现
test_server_discovery() {
    log_info "测试Matrix服务器发现..."

    local well_known_url="https://${DOMAIN}/.well-known/matrix/server"
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$well_known_url" || echo "000")

    if [[ "$response" == "200" ]]; then
        log_info "服务器发现配置正常"
    else
        log_warn "服务器发现配置可能有问题 (HTTP $response)"
        log_warn "请确保在您的Web服务器上配置了 .well-known/matrix/server"
    fi
}

# 测试Matrix客户端发现
test_client_discovery() {
    log_info "测试Matrix客户端发现..."

    local well_known_url="https://${DOMAIN}/.well-known/matrix/client"
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$well_known_url" || echo "000")

    if [[ "$response" == "200" ]]; then
        log_info "客户端发现配置正常"
    else
        log_warn "客户端发现配置可能有问题 (HTTP $response)"
        log_warn "请确保在您的Web服务器上配置了 .well-known/matrix/client"
    fi
}

# 测试Matrix API
test_matrix_api() {
    log_info "测试Matrix API..."

    local api_url="https://${MATRIX_DOMAIN}:${HTTPS_PORT}/_matrix/client/versions"
    local response=$(curl -s -k -o /dev/null -w "%{http_code}" "$api_url" || echo "000")

    if [[ "$response" == "200" ]]; then
        log_info "Matrix API响应正常"
    else
        log_error "Matrix API响应异常 (HTTP $response)"
    fi
}

# 测试TURN服务器
test_turn_server() {
    log_info "测试TURN服务器..."

    # 简单的UDP连接测试
    if command -v nc >/dev/null 2>&1; then
        if timeout 5 nc -u -z localhost $TURN_PORT; then
            log_info "TURN服务器UDP端口响应正常"
        else
            log_warn "TURN服务器UDP端口可能有问题"
        fi
    else
        log_warn "未安装netcat，跳过TURN服务器测试"
    fi
}

# 在线联邦测试
online_federation_test() {
    log_info "在线联邦测试建议："
    echo
    echo "请访问以下网站进行完整的联邦测试："
    echo "1. Matrix联邦测试器: https://federationtester.matrix.org/"
    echo "   输入您的域名: $DOMAIN"
    echo
    echo "2. Matrix服务器状态检查: https://matrix.org/docs/guides/server-discovery"
    echo
    echo "3. 手动测试命令："
    echo "   curl -k https://${MATRIX_DOMAIN}:${HTTPS_PORT}/_matrix/client/versions"
    echo
}

# 主函数
main() {
    log_info "开始Matrix联邦测试..."
    echo

    test_server_discovery
    test_client_discovery
    test_matrix_api
    test_turn_server

    echo
    online_federation_test

    log_info "测试完成"
}

main "$@"
EOF

# 设置脚本权限并执行
(venv) matrix@server:/opt/matrix$ chmod +x scripts/test-federation.sh
(venv) matrix@server:/opt/matrix$ ./scripts/test-federation.sh
```

### 6.3 创建系统服务（可选）

为了让Matrix服务在系统启动时自动运行，可以创建systemd用户服务：

```bash
# 创建systemd用户服务目录
(venv) matrix@server:/opt/matrix$ mkdir -p ~/.config/systemd/user

# 创建Matrix Pod服务文件
(venv) matrix@server:/opt/matrix$ cat > ~/.config/systemd/user/matrix-pod.service << 'EOF'
[Unit]
Description=Matrix Homeserver Pod
After=network.target

[Service]
Type=forking
RemainAfterExit=yes
ExecStart=/opt/matrix/scripts/manage-pod.sh start
ExecStop=/opt/matrix/scripts/manage-pod.sh stop
ExecReload=/opt/matrix/scripts/manage-pod.sh restart
TimeoutStartSec=300
TimeoutStopSec=120
Restart=on-failure
RestartSec=30

[Install]
WantedBy=default.target
EOF

# 重新加载systemd配置
(venv) matrix@server:/opt/matrix$ systemctl --user daemon-reload

# 启用服务（开机自启）
(venv) matrix@server:/opt/matrix$ systemctl --user enable matrix-pod.service

# 启动服务
(venv) matrix@server:/opt/matrix$ systemctl --user start matrix-pod.service

# 查看服务状态
(venv) matrix@server:/opt/matrix$ systemctl --user status matrix-pod.service
```

---

## 🔧 第七步：故障排除指南

### 7.1 常见问题诊断

#### 问题1：容器启动失败

**症状**：Pod或容器无法启动

**诊断步骤**：
```bash
# 查看Pod状态
(venv) matrix@server:/opt/matrix$ podman pod ps

# 查看容器状态
(venv) matrix@server:/opt/matrix$ podman ps -a

# 查看容器日志
(venv) matrix@server:/opt/matrix$ podman logs matrix-postgres
(venv) matrix@server:/opt/matrix$ podman logs matrix-synapse
```

**常见解决方案**：
- 检查端口是否被占用：`sudo netstat -tlnp | grep :8448`
- 检查磁盘空间：`df -h`
- 检查内存使用：`free -h`
- 重新创建Pod：`./scripts/manage-pod.sh create-pod`

#### 问题2：数据库连接失败

**症状**：Synapse无法连接到PostgreSQL

**诊断步骤**：
```bash
# 检查PostgreSQL容器状态
(venv) matrix@server:/opt/matrix$ podman exec matrix-postgres pg_isready -U synapse

# 检查数据库配置
(venv) matrix@server:/opt/matrix$ podman exec matrix-postgres psql -U synapse -d synapse -c "\l"

# 查看PostgreSQL日志
(venv) matrix@server:/opt/matrix$ podman logs matrix-postgres
```

**解决方案**：
- 验证数据库密码是否正确
- 检查homeserver.yaml中的数据库配置
- 重启PostgreSQL容器：`podman restart matrix-postgres`

#### 问题3：SSL证书问题

**症状**：HTTPS访问失败或证书错误

**诊断步骤**：
```bash
# 检查证书文件
(venv) matrix@server:/opt/matrix$ ls -la data/acme/

# 验证证书有效性
(venv) matrix@server:/opt/matrix$ openssl x509 -in data/acme/fullchain.cer -text -noout

# 测试SSL连接
(venv) matrix@server:/opt/matrix$ openssl s_client -connect localhost:8448 -servername matrix.example.com
```

**解决方案**：
- 重新申请证书：参考第四步证书管理
- 检查证书符号链接是否正确
- 重启Nginx容器：`podman restart matrix-nginx`

#### 问题4：联邦通信失败

**症状**：无法与其他Matrix服务器通信

**诊断步骤**：
```bash
# 测试联邦API
(venv) matrix@server:/opt/matrix$ curl -k https://matrix.example.com:8448/_matrix/federation/v1/version

# 检查防火墙设置
(venv) matrix@server:/opt/matrix$ sudo ufw status

# 检查路由器端口转发
(venv) matrix@server:/opt/matrix$ nmap -p 8448 your-public-ip
```

**解决方案**：
- 配置路由器端口转发：8448 -> 服务器IP:8448
- 检查DNS记录是否正确
- 配置.well-known文件（如果需要）

### 7.2 性能优化建议

#### 数据库优化

```bash
# 创建数据库优化脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/optimize-database.sh << 'EOF'
#!/bin/bash
# PostgreSQL性能优化脚本

set -euo pipefail

log_info() { echo "[INFO] $*"; }

# 优化PostgreSQL配置
optimize_postgres() {
    log_info "优化PostgreSQL配置..."

    # 连接到数据库执行优化
    podman exec matrix-postgres psql -U synapse -d synapse << 'SQL'
-- 创建索引以提高查询性能
CREATE INDEX CONCURRENTLY IF NOT EXISTS events_stream_ordering_idx ON events (stream_ordering);
CREATE INDEX CONCURRENTLY IF NOT EXISTS event_auth_chain_id_idx ON event_auth_chains (chain_id);

-- 更新表统计信息
ANALYZE;

-- 清理无用数据
VACUUM ANALYZE;
SQL

    log_info "数据库优化完成"
}

optimize_postgres
EOF

# 设置权限并执行
(venv) matrix@server:/opt/matrix$ chmod +x scripts/optimize-database.sh
```

#### 日志管理

```bash
# 创建日志清理脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/cleanup-logs.sh << 'EOF'
#!/bin/bash
# 日志清理脚本

set -euo pipefail

LOG_DIR="/opt/matrix/data"
DAYS_TO_KEEP=7

log_info() { echo "[INFO] $*"; }

# 清理旧日志
cleanup_logs() {
    log_info "清理超过 $DAYS_TO_KEEP 天的日志文件..."

    # 清理Nginx日志
    find $LOG_DIR/nginx/logs -name "*.log" -mtime +$DAYS_TO_KEEP -delete

    # 清理Coturn日志
    find $LOG_DIR/coturn/logs -name "*.log" -mtime +$DAYS_TO_KEEP -delete

    # 清理Synapse日志
    find $LOG_DIR/synapse/logs -name "*.log.*" -mtime +$DAYS_TO_KEEP -delete

    log_info "日志清理完成"
}

cleanup_logs
EOF

# 设置权限
(venv) matrix@server:/opt/matrix$ chmod +x scripts/cleanup-logs.sh

# 添加到crontab（每周执行一次）
(venv) matrix@server:/opt/matrix$ (crontab -l 2>/dev/null; echo "0 2 * * 0 /opt/matrix/scripts/cleanup-logs.sh") | crontab -
```

### 7.3 备份和恢复

#### 创建备份脚本

```bash
# 创建备份脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/backup-matrix.sh << 'EOF'
#!/bin/bash
# Matrix Homeserver备份脚本

set -euo pipefail

# 配置变量
BACKUP_DIR="/opt/matrix/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="matrix_backup_$DATE"

log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*"; }

# 创建备份目录
mkdir -p $BACKUP_DIR/$BACKUP_NAME

# 备份数据库
backup_database() {
    log_info "备份PostgreSQL数据库..."

    podman exec matrix-postgres pg_dump -U synapse synapse > $BACKUP_DIR/$BACKUP_NAME/synapse_db.sql

    if [[ $? -eq 0 ]]; then
        log_info "数据库备份完成"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 备份配置文件
backup_configs() {
    log_info "备份配置文件..."

    cp -r config/ $BACKUP_DIR/$BACKUP_NAME/

    log_info "配置文件备份完成"
}

# 备份媒体文件
backup_media() {
    log_info "备份媒体文件..."

    if [[ -d data/synapse/media_store ]]; then
        tar -czf $BACKUP_DIR/$BACKUP_NAME/media_store.tar.gz -C data/synapse media_store/
        log_info "媒体文件备份完成"
    else
        log_info "媒体目录不存在，跳过备份"
    fi
}

# 备份签名密钥
backup_keys() {
    log_info "备份签名密钥..."

    if [[ -f data/synapse/signing.key ]]; then
        cp data/synapse/signing.key $BACKUP_DIR/$BACKUP_NAME/
        log_info "签名密钥备份完成"
    else
        log_info "签名密钥不存在，跳过备份"
    fi
}

# 创建备份信息文件
create_backup_info() {
    cat > $BACKUP_DIR/$BACKUP_NAME/backup_info.txt << EOF
Matrix Homeserver备份信息
========================
备份时间: $(date)
备份版本: $BACKUP_NAME
服务器名称: $(grep server_name config/homeserver.yaml | cut -d'"' -f2)
Synapse版本: $(podman exec matrix-synapse python -m synapse.app.homeserver --version 2>/dev/null || echo "未知")

备份内容:
- PostgreSQL数据库
- 配置文件
- 媒体文件
- 签名密钥

恢复说明:
1. 停止Matrix服务
2. 恢复数据库: psql -U synapse synapse < synapse_db.sql
3. 恢复配置文件: cp -r config/* /opt/matrix/config/
4. 恢复媒体文件: tar -xzf media_store.tar.gz -C /opt/matrix/data/synapse/
5. 恢复签名密钥: cp signing.key /opt/matrix/data/synapse/
6. 重启Matrix服务
EOF
}

# 压缩备份
compress_backup() {
    log_info "压缩备份文件..."

    cd $BACKUP_DIR
    tar -czf $BACKUP_NAME.tar.gz $BACKUP_NAME/
    rm -rf $BACKUP_NAME/

    log_info "备份压缩完成: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理超过30天的旧备份..."

    find $BACKUP_DIR -name "matrix_backup_*.tar.gz" -mtime +30 -delete

    log_info "旧备份清理完成"
}

# 主函数
main() {
    log_info "开始Matrix备份流程..."

    backup_database
    backup_configs
    backup_media
    backup_keys
    create_backup_info
    compress_backup
    cleanup_old_backups

    log_info "备份流程完成"
    log_info "备份文件: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
}

main "$@"
EOF

# 设置权限
(venv) matrix@server:/opt/matrix$ chmod +x scripts/backup-matrix.sh

# 添加到crontab（每天凌晨3点备份）
(venv) matrix@server:/opt/matrix$ (crontab -l 2>/dev/null; echo "0 3 * * * /opt/matrix/scripts/backup-matrix.sh >> /var/log/matrix-backup.log 2>&1") | crontab -
```

---

## 📚 第八步：日常维护指南

### 8.1 常用管理命令

```bash
# 查看服务状态
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh status

# 重启所有服务
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh restart

# 查看特定服务日志
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh logs synapse

# 进入容器进行调试
(venv) matrix@server:/opt/matrix$ podman exec -it matrix-synapse /bin/bash

# 查看资源使用情况
(venv) matrix@server:/opt/matrix$ podman stats

# 更新容器镜像
(venv) matrix@server:/opt/matrix$ podman pull matrixdotorg/synapse:latest
(venv) matrix@server:/opt/matrix$ ./scripts/manage-pod.sh restart
```

### 8.2 监控和告警

创建简单的监控脚本：

```bash
# 创建监控脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/monitor-matrix.sh << 'EOF'
#!/bin/bash
# Matrix服务监控脚本

set -euo pipefail

# 配置
ALERT_EMAIL="<EMAIL>"  # 替换为您的邮箱
LOG_FILE="/var/log/matrix-monitor.log"

log_info() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*" | tee -a $LOG_FILE; }
log_error() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*" | tee -a $LOG_FILE; }

# 检查服务状态
check_services() {
    local failed_services=()

    # 检查Pod状态
    if ! podman pod exists matrix-pod; then
        failed_services+=("matrix-pod")
    fi

    # 检查各个容器
    for service in postgres redis synapse nginx coturn; do
        if ! podman ps --format "{{.Names}}" | grep -q "matrix-$service"; then
            failed_services+=("matrix-$service")
        fi
    done

    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_error "以下服务异常: ${failed_services[*]}"
        return 1
    else
        log_info "所有服务运行正常"
        return 0
    fi
}

# 检查磁盘空间
check_disk_space() {
    local usage=$(df /opt/matrix | awk 'NR==2 {print $5}' | sed 's/%//')

    if [[ $usage -gt 80 ]]; then
        log_error "磁盘空间不足: ${usage}%"
        return 1
    else
        log_info "磁盘空间正常: ${usage}%"
        return 0
    fi
}

# 检查内存使用
check_memory() {
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')

    if [[ $usage -gt 90 ]]; then
        log_error "内存使用过高: ${usage}%"
        return 1
    else
        log_info "内存使用正常: ${usage}%"
        return 0
    fi
}

# 主函数
main() {
    log_info "开始Matrix服务监控检查..."

    local errors=0

    check_services || ((errors++))
    check_disk_space || ((errors++))
    check_memory || ((errors++))

    if [[ $errors -gt 0 ]]; then
        log_error "发现 $errors 个问题"
        # 这里可以添加邮件告警功能
        # echo "Matrix服务监控发现问题，请检查日志: $LOG_FILE" | mail -s "Matrix监控告警" $ALERT_EMAIL
    else
        log_info "所有检查通过"
    fi

    log_info "监控检查完成"
}

main "$@"
EOF

# 设置权限
(venv) matrix@server:/opt/matrix$ chmod +x scripts/monitor-matrix.sh

# 添加到crontab（每5分钟检查一次）
(venv) matrix@server:/opt/matrix$ (crontab -l 2>/dev/null; echo "*/5 * * * * /opt/matrix/scripts/monitor-matrix.sh") | crontab -
```

### 8.3 更新和升级

```bash
# 创建更新脚本
(venv) matrix@server:/opt/matrix$ cat > scripts/update-matrix.sh << 'EOF'
#!/bin/bash
# Matrix服务更新脚本

set -euo pipefail

log_info() { echo "[INFO] $*"; }
log_warn() { echo "[WARN] $*"; }

# 备份当前配置
backup_before_update() {
    log_info "更新前备份..."
    ./scripts/backup-matrix.sh
}

# 更新容器镜像
update_images() {
    log_info "更新容器镜像..."

    podman pull postgres:15-alpine
    podman pull redis:7-alpine
    podman pull matrixdotorg/synapse:latest
    podman pull nginx:alpine
    podman pull coturn/coturn:latest

    log_info "镜像更新完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."

    ./scripts/manage-pod.sh stop
    sleep 10
    ./scripts/manage-pod.sh start

    log_info "服务重启完成"
}

# 验证更新
verify_update() {
    log_info "验证更新..."

    sleep 30  # 等待服务启动

    if ./scripts/test-federation.sh; then
        log_info "更新验证成功"
    else
        log_warn "更新验证失败，请检查服务状态"
    fi
}

# 主函数
main() {
    log_info "开始Matrix更新流程..."

    backup_before_update
    update_images
    restart_services
    verify_update

    log_info "更新流程完成"
}

main "$@"
EOF

# 设置权限
(venv) matrix@server:/opt/matrix$ chmod +x scripts/update-matrix.sh
```

---

## 🎯 总结

恭喜！您已经成功完成了Matrix Homeserver的Podman手动部署。本指南涵盖了：

### ✅ 已完成的配置
- ✅ Podman环境安装和配置
- ✅ Python虚拟环境设置
- ✅ 完整的服务配置文件
- ✅ Podman Pod部署方案
- ✅ SSL证书管理
- ✅ 系统服务配置
- ✅ 监控和备份脚本

### 🔧 核心服务
- **PostgreSQL**: 数据库服务
- **Redis**: 缓存服务
- **Synapse**: Matrix核心服务
- **Nginx**: 反向代理
- **Coturn**: TURN/STUN服务器

### 📝 重要提醒
1. **定期备份**: 使用提供的备份脚本
2. **监控服务**: 定期检查服务状态
3. **更新维护**: 定期更新容器镜像
4. **安全配置**: 定期更新证书和密码
5. **日志管理**: 定期清理旧日志文件

### 🆘 获取帮助
如果遇到问题，请：
1. 查看相关服务日志
2. 参考故障排除部分
3. 使用提供的测试脚本
4. 检查配置文件是否正确

### 🔗 有用的资源
- Matrix官方文档: https://matrix.org/docs/
- Synapse管理指南: https://matrix-org.github.io/synapse/
- Podman官方文档: https://podman.io/docs/
- Element客户端: https://element.io/

您的Matrix Homeserver现在已经准备就绪，可以开始使用了！