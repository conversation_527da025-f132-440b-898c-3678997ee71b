# Matrix Homeserver 脚本功能详细说明

## 📋 目录

1. [脚本概述](#脚本概述)
2. [部署脚本详解](#部署脚本详解)
3. [运维脚本详解](#运维脚本详解)
4. [管理脚本详解](#管理脚本详解)
5. [工具脚本详解](#工具脚本详解)
6. [脚本执行流程](#脚本执行流程)
7. [脚本调试技巧](#脚本调试技巧)

---

## 📖 脚本概述

Matrix Homeserver项目包含多个自动化脚本，每个脚本都有特定的功能和用途：

### 脚本分类

| 类型 | 脚本名称 | 主要功能 | 执行频率 |
|------|----------|----------|----------|
| 🚀 部署 | setup.sh | 初始化部署环境 | 一次性 |
| 🚀 部署 | deploy.sh | 外部指路牌部署 | 一次性 |
| 🔧 运维 | ip_watchdog.sh | 动态IP监控同步 | 每分钟 |
| 🔧 运维 | certificate_manager.sh | SSL证书管理 | 每天 |
| 🔧 运维 | init_certificate_links.sh | 证书符号链接管理 | 按需 |
| 🔧 运维 | health_check.sh | 系统健康检查 | 每5分钟 |
| 👨‍💼 管理 | admin.sh | Matrix管理工具 | 按需 |
| 💾 维护 | backup.sh | 数据备份 | 每天 |
| 🔧 工具 | routeros_setup.sh | RouterOS配置 | 按需 |

### 脚本设计原则

✅ **幂等性**：多次执行产生相同结果  
✅ **错误处理**：完善的错误检测和恢复机制  
✅ **日志记录**：详细的操作日志和状态信息  
✅ **参数验证**：输入参数的严格验证  
✅ **安全性**：敏感信息的安全处理  

---

## 🚀 部署脚本详解

### setup.sh - 初始化部署脚本

#### 脚本功能概述
`setup.sh`是Matrix Homeserver的核心部署脚本，负责完整的初始化部署流程。

#### 主要功能模块

```bash
#!/bin/bash
# ================================================================
# Matrix Homeserver 初始化部署脚本
# ================================================================
# 功能：自动化部署Matrix Homeserver的完整环境
# 作者：Matrix Homeserver部署项目
# 版本：1.0.0
# 用法：./scripts/setup.sh [选项]
# ================================================================

# 脚本执行流程：
# 1. 环境检查 → 2. 依赖安装 → 3. 目录创建 → 4. 配置生成 
# → 5. 证书初始化 → 6. IP同步设置 → 7. 服务启动 → 8. 定时任务配置

set -euo pipefail  # 严格模式：遇到错误立即退出，未定义变量报错，管道错误传播

# ================================================================
# 全局变量定义
# ================================================================

# 脚本路径和项目目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="${PROJECT_DIR}/config"
DATA_DIR="${PROJECT_DIR}/data"

# 日志配置
LOG_FILE="/var/log/matrix/setup.log"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR

# 功能开关（可通过命令行参数控制）
FORCE_SETUP=false          # 是否强制重新初始化
SKIP_DEPS=false            # 是否跳过依赖检查
SKIP_CONFIG=false          # 是否跳过配置文件生成
SKIP_CERTS=false           # 是否跳过证书申请
SKIP_SERVICES=false        # 是否跳过服务启动

# 颜色定义（用于终端输出）
RED='\033[0;31m'           # 红色（错误）
GREEN='\033[0;32m'         # 绿色（成功）
YELLOW='\033[1;33m'        # 黄色（警告）
BLUE='\033[0;34m'          # 蓝色（信息）
NC='\033[0m'               # 无颜色（重置）

# ================================================================
# 日志记录函数
# ================================================================

# 初始化日志系统
init_logging() {
    local log_file="$1"
    
    # 创建日志目录
    mkdir -p "$(dirname "$log_file")"
    
    # 设置日志文件权限
    touch "$log_file"
    chmod 644 "$log_file"
    
    # 记录脚本开始执行
    log_info "Matrix Homeserver初始化部署开始"
    log_info "脚本版本: 1.0.0"
    log_info "执行时间: $(date)"
    log_info "执行用户: $(whoami)"
    log_info "项目目录: $PROJECT_DIR"
}

# 通用日志记录函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 写入日志文件
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    # 根据级别输出到终端（带颜色）
    case "$level" in
        "DEBUG")
            [[ "$LOG_LEVEL" == "DEBUG" ]] && echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
    esac
}

# 便捷的日志记录函数
log_debug() { log_message "DEBUG" "$*"; }
log_info() { log_message "INFO" "$*"; }
log_warn() { log_message "WARN" "$*"; }
log_error() { log_message "ERROR" "$*"; }

# 致命错误处理（记录日志并退出）
log_fatal() {
    log_error "$*"
    log_error "脚本执行失败，退出"
    exit 1
}

# ================================================================
# 命令行参数解析
# ================================================================

# 显示帮助信息
show_help() {
    cat << EOF
Matrix Homeserver 初始化部署脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -f, --force             强制重新初始化（覆盖现有配置）
  --skip-deps             跳过依赖检查和安装
  --skip-config           跳过配置文件生成
  --skip-certs            跳过SSL证书申请
  --skip-services         跳过服务启动
  -v, --verbose           显示详细输出（DEBUG级别）

示例:
  $0                      # 完整初始化部署
  $0 --force              # 强制重新初始化
  $0 --skip-deps          # 跳过依赖检查
  $0 --skip-certs         # 跳过证书申请（手动管理证书时）

注意:
  - 首次运行需要root权限安装系统依赖
  - 确保已正确配置deployment.env文件
  - 建议在执行前备份现有数据

EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_SETUP=true
                log_info "启用强制重新初始化模式"
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                log_info "跳过依赖检查和安装"
                shift
                ;;
            --skip-config)
                SKIP_CONFIG=true
                log_info "跳过配置文件生成"
                shift
                ;;
            --skip-certs)
                SKIP_CERTS=true
                log_info "跳过SSL证书申请"
                shift
                ;;
            --skip-services)
                SKIP_SERVICES=true
                log_info "跳过服务启动"
                shift
                ;;
            -v|--verbose)
                LOG_LEVEL="DEBUG"
                log_info "启用详细输出模式"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# ================================================================
# 环境检查函数
# ================================================================

# 检查运行环境
check_environment() {
    log_info "开始环境检查"
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_fatal "无法确定操作系统类型"
    fi
    
    local os_name=$(grep '^NAME=' /etc/os-release | cut -d'"' -f2)
    local os_version=$(grep '^VERSION=' /etc/os-release | cut -d'"' -f2)
    log_info "操作系统: $os_name $os_version"
    
    # 检查系统架构
    local arch=$(uname -m)
    if [[ "$arch" != "x86_64" ]]; then
        log_warn "检测到非x86_64架构: $arch，可能存在兼容性问题"
    fi
    log_info "系统架构: $arch"
    
    # 检查可用内存
    local total_mem=$(free -m | awk 'NR==2{print $2}')
    if [[ $total_mem -lt 4096 ]]; then
        log_warn "系统内存不足4GB (当前: ${total_mem}MB)，可能影响性能"
    fi
    log_info "系统内存: ${total_mem}MB"
    
    # 检查可用磁盘空间
    local available_space=$(df "$PROJECT_DIR" | awk 'NR==2{print $4}')
    local available_gb=$((available_space / 1024 / 1024))
    if [[ $available_gb -lt 50 ]]; then
        log_warn "可用磁盘空间不足50GB (当前: ${available_gb}GB)"
    fi
    log_info "可用磁盘空间: ${available_gb}GB"
    
    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log_fatal "网络连接检查失败，无法访问互联网"
    fi
    log_info "网络连接正常"
    
    # 检查DNS解析
    if ! nslookup google.com >/dev/null 2>&1; then
        log_warn "DNS解析可能存在问题"
    fi
    log_info "DNS解析正常"
    
    log_info "环境检查完成"
    return 0
}

# 检查用户权限
check_permissions() {
    log_info "检查用户权限"
    
    # 检查是否为root用户（某些操作需要）
    if [[ $EUID -eq 0 ]]; then
        log_warn "当前以root用户运行，建议使用普通用户"
    fi
    
    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_warn "当前用户没有sudo权限，某些操作可能失败"
    fi
    
    # 检查Docker组权限
    if ! groups | grep -q docker; then
        log_warn "当前用户不在docker组中，需要使用sudo运行Docker命令"
    fi
    
    # 检查项目目录权限
    if [[ ! -w "$PROJECT_DIR" ]]; then
        log_fatal "没有项目目录的写入权限: $PROJECT_DIR"
    fi
    
    log_info "权限检查完成"
    return 0
}

# ================================================================
# 依赖检查和安装函数
# ================================================================

# 检查系统依赖
check_dependencies() {
    if [[ "$SKIP_DEPS" == "true" ]]; then
        log_info "跳过依赖检查"
        return 0
    fi
    
    log_info "开始依赖检查"
    
    # 必需的系统命令
    local required_commands=(
        "curl"      # 网络请求工具
        "wget"      # 文件下载工具
        "git"       # 版本控制工具
        "docker"    # 容器运行时
        "docker-compose"  # 容器编排工具
        "openssl"   # 加密工具
        "python3"   # Python解释器
        "pip3"      # Python包管理器
    )
    
    local missing_commands=()
    
    # 检查每个必需命令
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_commands+=("$cmd")
            log_warn "缺少必需命令: $cmd"
        else
            local version=$($cmd --version 2>&1 | head -n1 || echo "未知版本")
            log_debug "已安装: $cmd ($version)"
        fi
    done
    
    # 如果有缺失的命令，尝试安装
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_info "检测到缺失的依赖，尝试自动安装"
        install_dependencies "${missing_commands[@]}"
    fi
    
    # 检查Docker服务状态
    if ! systemctl is-active --quiet docker; then
        log_info "启动Docker服务"
        sudo systemctl start docker
        sudo systemctl enable docker
    fi
    
    # 检查acme.sh
    if ! command -v acme.sh &> /dev/null; then
        log_info "安装acme.sh"
        install_acme_sh
    fi
    
    log_info "依赖检查完成"
    return 0
}

# 安装系统依赖
install_dependencies() {
    local packages=("$@")
    
    log_info "安装系统依赖: ${packages[*]}"
    
    # 检测包管理器
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu系统
        sudo apt-get update
        sudo apt-get install -y "${packages[@]}"
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL系统
        sudo yum install -y "${packages[@]}"
    elif command -v dnf &> /dev/null; then
        # Fedora系统
        sudo dnf install -y "${packages[@]}"
    else
        log_fatal "无法确定包管理器，请手动安装依赖: ${packages[*]}"
    fi
    
    log_info "系统依赖安装完成"
}

# 安装acme.sh
install_acme_sh() {
    log_info "安装acme.sh SSL证书管理工具"
    
    # 下载并安装acme.sh
    curl https://get.acme.sh | sh -s email=<EMAIL>
    
    # 重新加载shell配置
    source ~/.bashrc
    
    # 设置默认CA
    ~/.acme.sh/acme.sh --set-default-ca --server letsencrypt
    
    log_info "acme.sh安装完成"
}

# ================================================================
# 目录结构创建函数
# ================================================================

# 创建项目目录结构
create_directory_structure() {
    log_info "创建项目目录结构"

    # 定义需要创建的目录列表
    local directories=(
        # 配置目录
        "$CONFIG_DIR"

        # 数据目录
        "$DATA_DIR"
        "$DATA_DIR/nginx/certs"
        "$DATA_DIR/nginx/logs"
        "$DATA_DIR/coturn/conf"
        "$DATA_DIR/coturn/certs"
        "$DATA_DIR/coturn/logs"
        "$DATA_DIR/synapse"
        "$DATA_DIR/postgres"
        "$DATA_DIR/redis"
        "$DATA_DIR/acme"
        "$DATA_DIR/logs"
        "$DATA_DIR/backup"

        # 日志目录
        "/var/log/matrix"
    )

    # 创建目录并设置权限
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_debug "创建目录: $dir"
            mkdir -p "$dir"
        fi

        # 设置目录权限
        if [[ "$dir" == "/var/log/matrix" ]]; then
            # 系统日志目录需要特殊权限
            sudo mkdir -p "$dir"
            sudo chown matrix:matrix "$dir"
            sudo chmod 755 "$dir"
        else
            # 项目目录使用标准权限
            chown matrix:matrix "$dir"
            chmod 755 "$dir"
        fi
    done

    log_info "目录结构创建完成"
    return 0
}

# ================================================================
# 配置文件生成函数
# ================================================================

# 加载和验证配置
load_configuration() {
    log_info "加载配置文件"

    local config_file="$CONFIG_DIR/deployment.env"

    # 检查配置文件是否存在
    if [[ ! -f "$config_file" ]]; then
        log_fatal "配置文件不存在: $config_file"
    fi

    # 加载配置文件
    source "$config_file"

    # 验证必需的配置变量
    local required_vars=(
        "DOMAIN"
        "SUBDOMAIN_MATRIX"
        "HTTPS_PORT"
        "DB_PASSWORD"
        "CLOUDFLARE_API_TOKEN"
        "CLOUDFLARE_ZONE_ID"
        "COTURN_SHARED_SECRET"
    )

    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_fatal "缺少必需的配置变量: ${missing_vars[*]}"
    fi

    # 验证配置值的格式
    validate_configuration

    log_info "配置加载完成"
    return 0
}

# 验证配置格式
validate_configuration() {
    log_debug "验证配置格式"

    # 验证域名格式
    if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$ ]]; then
        log_fatal "域名格式无效: $DOMAIN"
    fi

    # 验证端口号
    if [[ ! "$HTTPS_PORT" =~ ^[0-9]+$ ]] || [[ "$HTTPS_PORT" -lt 1 ]] || [[ "$HTTPS_PORT" -gt 65535 ]]; then
        log_fatal "端口号无效: $HTTPS_PORT"
    fi

    # 验证密码强度
    if [[ ${#DB_PASSWORD} -lt 16 ]]; then
        log_warn "数据库密码长度不足16位，建议使用更强的密码"
    fi

    # 验证Cloudflare API Token格式
    if [[ ! "$CLOUDFLARE_API_TOKEN" =~ ^[a-zA-Z0-9_-]{40}$ ]]; then
        log_warn "Cloudflare API Token格式可能不正确"
    fi

    log_debug "配置格式验证完成"
}

# 生成配置文件
generate_configuration_files() {
    if [[ "$SKIP_CONFIG" == "true" ]]; then
        log_info "跳过配置文件生成"
        return 0
    fi

    log_info "生成配置文件"

    # 生成Synapse配置文件
    generate_synapse_config

    # 生成Nginx配置文件
    generate_nginx_config

    # 生成Coturn配置文件
    generate_coturn_config

    # 生成PostgreSQL初始化脚本
    generate_postgres_config

    log_info "配置文件生成完成"
    return 0
}

# 生成Synapse配置文件
generate_synapse_config() {
    log_debug "生成Synapse配置文件"

    local template_file="$CONFIG_DIR/homeserver.yaml.template"
    local output_file="$CONFIG_DIR/homeserver.yaml"

    if [[ ! -f "$template_file" ]]; then
        log_fatal "Synapse配置模板不存在: $template_file"
    fi

    # 使用envsubst替换模板中的变量
    envsubst < "$template_file" > "$output_file"

    # 设置文件权限
    chmod 644 "$output_file"

    log_debug "Synapse配置文件生成完成: $output_file"
}

# 生成Nginx配置文件
generate_nginx_config() {
    log_debug "生成Nginx配置文件"

    # 生成主配置文件
    local nginx_template="$CONFIG_DIR/nginx.conf.template"
    local nginx_output="$CONFIG_DIR/nginx.conf"

    if [[ -f "$nginx_template" ]]; then
        envsubst < "$nginx_template" > "$nginx_output"
        chmod 644 "$nginx_output"
    fi

    # 生成站点配置文件
    local site_template="$CONFIG_DIR/matrix.conf.template"
    local site_output="$CONFIG_DIR/matrix.conf"

    if [[ -f "$site_template" ]]; then
        envsubst < "$site_template" > "$site_output"
        chmod 644 "$site_output"
    fi

    log_debug "Nginx配置文件生成完成"
}

# 生成Coturn配置文件
generate_coturn_config() {
    log_debug "生成Coturn配置文件"

    local template_file="$CONFIG_DIR/turnserver.conf.template"
    local output_file="$DATA_DIR/coturn/conf/turnserver.conf"

    if [[ ! -f "$template_file" ]]; then
        log_warn "Coturn配置模板不存在: $template_file"
        return 0
    fi

    # 替换模板变量
    envsubst < "$template_file" > "$output_file"

    # 设置文件权限（Coturn配置文件包含敏感信息）
    chmod 600 "$output_file"

    log_debug "Coturn配置文件生成完成: $output_file"
}

# 生成PostgreSQL初始化脚本
generate_postgres_config() {
    log_debug "生成PostgreSQL初始化脚本"

    local init_script="$CONFIG_DIR/postgres-init.sql"

    # 创建数据库初始化脚本
    cat > "$init_script" << EOF
-- PostgreSQL初始化脚本
-- 为Matrix Synapse创建数据库和用户

-- 创建数据库（如果不存在）
CREATE DATABASE ${DB_NAME}
    ENCODING 'UTF8'
    LC_COLLATE 'C'
    LC_CTYPE 'C'
    TEMPLATE template0;

-- 创建用户（如果不存在）
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '${DB_USER}') THEN
        CREATE USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';
    END IF;
END
\$\$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${DB_USER};
EOF

    chmod 644 "$init_script"
    log_debug "PostgreSQL初始化脚本生成完成: $init_script"
}
```

---

## 🔧 运维脚本详解

### ip_watchdog.sh - 动态IP监控脚本

#### 脚本功能概述
`ip_watchdog.sh`是动态IP监控和同步脚本，负责检测公网IP变化并自动更新DNS记录和服务配置。

#### 核心功能模块

```bash
#!/bin/bash
# ================================================================
# Matrix Homeserver 动态IP监控脚本
# ================================================================
# 功能：监控动态公网IP变化，自动更新DNS记录和Coturn配置
# 执行频率：每分钟（通过cron任务）
# 支持：RouterOS API、Cloudflare DNS API
# ================================================================

set -euo pipefail

# ================================================================
# 脚本配置和变量
# ================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_DIR/config/deployment.env"
LOCK_FILE="/tmp/ip_watchdog.lock"
LOG_FILE="/var/log/matrix/ip_watchdog.log"

# IP监控配置
CHECK_ONLY=false           # 仅检查模式，不执行更新
FORCE_UPDATE=false         # 强制更新模式
VERBOSE=false              # 详细输出模式
MAX_RETRIES=3              # 最大重试次数
RETRY_DELAY=10             # 重试延迟（秒）

# IP获取方法优先级
IP_SOURCES=(
    "routeros"             # RouterOS API（优先）
    "external_service"     # 外部IP查询服务（备选）
)

# 外部IP查询服务列表
EXTERNAL_IP_SERVICES=(
    "https://ipv4.icanhazip.com/"
    "https://api.ipify.org"
    "https://checkip.amazonaws.com/"
    "https://ifconfig.me/ip"
)

# ================================================================
# 工具函数
# ================================================================

# 创建进程锁（防止重复执行）
create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local lock_pid=$(cat "$LOCK_FILE")
        if kill -0 "$lock_pid" 2>/dev/null; then
            log_warn "检测到另一个实例正在运行 (PID: $lock_pid)"
            exit 1
        else
            log_warn "发现过期的锁文件，清理中"
            rm -f "$LOCK_FILE"
        fi
    fi

    echo $$ > "$LOCK_FILE"
    log_debug "创建进程锁: $LOCK_FILE"
}

# 清理进程锁
cleanup_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
        log_debug "清理进程锁"
    fi
}

# 验证IP地址格式
validate_ip() {
    local ip="$1"

    # IPv4地址格式验证
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        # 检查每个八位组是否在0-255范围内
        IFS='.' read -ra ADDR <<< "$ip"
        for i in "${ADDR[@]}"; do
            if [[ $i -gt 255 ]]; then
                return 1
            fi
        done
        return 0
    fi

    return 1
}

# ================================================================
# IP获取函数
# ================================================================

# 从RouterOS获取WAN IP
get_ip_from_routeros() {
    log_debug "尝试从RouterOS获取IP地址"

    # 检查RouterOS配置
    if [[ -z "${ROUTEROS_HOST:-}" ]] || [[ -z "${ROUTEROS_USER:-}" ]]; then
        log_debug "RouterOS配置不完整，跳过"
        return 1
    fi

    # 检查RouterOS API客户端
    local routeros_client="$SCRIPT_DIR/utils/routeros_client.py"
    if [[ ! -f "$routeros_client" ]]; then
        log_debug "RouterOS客户端脚本不存在: $routeros_client"
        return 1
    fi

    # 构建API调用参数
    local args=(
        "--host" "$ROUTEROS_HOST"
        "--port" "${ROUTEROS_PORT:-8728}"
        "--user" "$ROUTEROS_USER"
        "--password" "$ROUTEROS_PASSWORD"
        "--interface" "${ROUTEROS_WAN_INTERFACE:-WAN}"
    )

    # 执行RouterOS API调用（设置超时）
    local ip
    if ip=$(timeout 15 python3 "$routeros_client" "${args[@]}" 2>/dev/null); then
        if validate_ip "$ip"; then
            log_debug "从RouterOS获取IP成功: $ip"
            echo "$ip"
            return 0
        else
            log_warn "RouterOS返回的IP格式无效: $ip"
        fi
    else
        log_debug "RouterOS API调用失败"
    fi

    return 1
}

# 从外部服务获取IP
get_ip_from_external_service() {
    log_debug "尝试从外部服务获取IP地址"

    for service in "${EXTERNAL_IP_SERVICES[@]}"; do
        log_debug "查询服务: $service"

        # 使用curl获取IP（设置超时和重试）
        local ip
        if ip=$(curl -s --max-time 10 --retry 2 "$service" 2>/dev/null); then
            # 清理返回的IP地址（去除空白字符）
            ip=$(echo "$ip" | tr -d '[:space:]')

            if validate_ip "$ip"; then
                log_debug "从外部服务获取IP成功: $ip (来源: $service)"
                echo "$ip"
                return 0
            else
                log_debug "外部服务返回的IP格式无效: $ip (来源: $service)"
            fi
        else
            log_debug "外部服务查询失败: $service"
        fi
    done

    return 1
}

# 获取当前公网IP
get_current_ip() {
    log_debug "开始获取当前公网IP"

    # 按优先级尝试不同的IP获取方法
    for source in "${IP_SOURCES[@]}"; do
        case "$source" in
            "routeros")
                if get_ip_from_routeros; then
                    return 0
                fi
                ;;
            "external_service")
                if get_ip_from_external_service; then
                    return 0
                fi
                ;;
        esac
    done

    log_error "所有IP获取方法都失败了"
    return 1
}

# ================================================================
# DNS更新函数
# ================================================================

# 更新Cloudflare DNS记录
update_dns_record() {
    local new_ip="$1"
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"

    log_info "更新DNS记录: $domain -> $new_ip"

    # 检查Cloudflare API配置
    if [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]] || [[ -z "${CLOUDFLARE_ZONE_ID:-}" ]]; then
        log_error "Cloudflare API配置不完整"
        return 1
    fi

    # 获取现有DNS记录
    local record_info
    record_info=$(curl -s -X GET \
        "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records?name=$domain&type=A" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json")

    # 检查API响应
    if [[ $(echo "$record_info" | jq -r '.success') != "true" ]]; then
        log_error "获取DNS记录失败: $(echo "$record_info" | jq -r '.errors[0].message')"
        return 1
    fi

    # 提取记录ID
    local record_id
    record_id=$(echo "$record_info" | jq -r '.result[0].id')

    if [[ "$record_id" == "null" ]]; then
        log_info "DNS记录不存在，创建新记录"
        create_dns_record "$domain" "$new_ip"
    else
        log_info "更新现有DNS记录 (ID: $record_id)"
        update_existing_dns_record "$record_id" "$domain" "$new_ip"
    fi
}

# 创建新的DNS记录
create_dns_record() {
    local domain="$1"
    local ip="$2"

    local response
    response=$(curl -s -X POST \
        "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{\"type\":\"A\",\"name\":\"$domain\",\"content\":\"$ip\",\"ttl\":300}")

    if [[ $(echo "$response" | jq -r '.success') == "true" ]]; then
        log_info "DNS记录创建成功: $domain -> $ip"
        return 0
    else
        log_error "DNS记录创建失败: $(echo "$response" | jq -r '.errors[0].message')"
        return 1
    fi
}

# 更新现有DNS记录
update_existing_dns_record() {
    local record_id="$1"
    local domain="$2"
    local ip="$3"

    local response
    response=$(curl -s -X PUT \
        "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records/$record_id" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{\"type\":\"A\",\"name\":\"$domain\",\"content\":\"$ip\",\"ttl\":300}")

    if [[ $(echo "$response" | jq -r '.success') == "true" ]]; then
        log_info "DNS记录更新成功: $domain -> $ip"
        return 0
    else
        log_error "DNS记录更新失败: $(echo "$response" | jq -r '.errors[0].message')"
        return 1
    fi
}

# ================================================================
# Coturn配置更新函数
# ================================================================

# 更新Coturn配置中的外部IP
update_coturn_config() {
    local new_ip="$1"
    local coturn_config="$DATA_DIR/coturn/conf/turnserver.conf"

    log_info "更新Coturn配置: external-ip=$new_ip"

    if [[ ! -f "$coturn_config" ]]; then
        log_error "Coturn配置文件不存在: $coturn_config"
        return 1
    fi

    # 备份原配置文件
    cp "$coturn_config" "${coturn_config}.backup.$(date +%Y%m%d_%H%M%S)"

    # 更新external-ip配置
    sed -i "s/^external-ip=.*/external-ip=$new_ip/" "$coturn_config"

    # 验证配置文件语法
    if ! turnserver --check-config -c "$coturn_config" >/dev/null 2>&1; then
        log_error "Coturn配置文件语法错误，恢复备份"
        cp "${coturn_config}.backup.$(date +%Y%m%d_%H%M%S)" "$coturn_config"
        return 1
    fi

    log_info "Coturn配置更新成功"
    return 0
}

# 重启Coturn服务
restart_coturn() {
    log_info "重启Coturn服务"

    if docker compose -f "$PROJECT_DIR/docker-compose.yml" restart coturn; then
        log_info "Coturn服务重启成功"

        # 等待服务启动
        sleep 5

        # 验证服务状态
        if docker compose -f "$PROJECT_DIR/docker-compose.yml" ps coturn | grep -q "Up"; then
            log_info "Coturn服务运行正常"
            return 0
        else
            log_error "Coturn服务启动失败"
            return 1
        fi
    else
        log_error "Coturn服务重启失败"
        return 1
    fi
}

# ================================================================
# IP记录管理函数
# ================================================================

# 获取上次记录的IP
get_last_ip() {
    local ip_file="$DATA_DIR/last_ip.txt"

    if [[ -f "$ip_file" ]]; then
        cat "$ip_file"
    else
        echo ""
    fi
}

# 保存当前IP
save_current_ip() {
    local ip="$1"
    local ip_file="$DATA_DIR/last_ip.txt"

    echo "$ip" > "$ip_file"
    log_debug "保存IP记录: $ip"
}

# 验证IP更新结果
verify_ip_update() {
    local expected_ip="$1"
    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"

    log_info "验证IP更新结果"

    # 等待DNS传播
    sleep 10

    # 查询DNS记录
    local resolved_ip
    if resolved_ip=$(nslookup "$domain" | grep -A1 "Name:" | tail -n1 | awk '{print $2}'); then
        if [[ "$resolved_ip" == "$expected_ip" ]]; then
            log_info "DNS解析验证成功: $domain -> $resolved_ip"
            return 0
        else
            log_warn "DNS解析不匹配: 期望 $expected_ip，实际 $resolved_ip"
            return 1
        fi
    else
        log_warn "DNS解析查询失败"
        return 1
    fi
}
```

### certificate_manager.sh - SSL证书管理脚本

#### 脚本功能概述
`certificate_manager.sh`是智能SSL证书管理脚本，负责证书的申请、续期、部署和验证。

#### 核心功能模块

```bash
#!/bin/bash
# ================================================================
# Matrix Homeserver SSL证书管理脚本
# ================================================================
# 功能：智能管理SSL证书的申请、续期和部署
# 执行频率：每天（通过cron任务）
# 支持：Let's Encrypt、ECC/RSA证书、三层符号链接架构
# ================================================================

set -euo pipefail

# ================================================================
# 脚本配置和变量
# ================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_DIR/config/deployment.env"
LOG_FILE="/var/log/matrix/certificate_manager.log"

# 证书管理配置
CHECK_EXPIRY=false         # 仅检查证书到期时间
CHECK_ONLY=false           # 仅检查模式，不执行操作
FORCE_RENEW=false          # 强制续期证书
VERBOSE=false              # 详细输出模式

# 证书配置
CERT_RENEWAL_DAYS=14       # 证书续期阈值（天）
ACME_HOME="/root/.acme.sh" # acme.sh安装目录
BACKUP_DIR="$PROJECT_DIR/data/backup/certificates"

# ================================================================
# 证书检查函数
# ================================================================

# 检查证书有效期
check_certificate_validity() {
    local domain="$1"
    local cert_file="$PROJECT_DIR/data/nginx/certs/fullchain.cer"

    log_debug "检查证书有效期: $domain"

    if [[ ! -f "$cert_file" ]]; then
        log_debug "证书文件不存在: $cert_file"
        echo "MISSING"
        return 0
    fi

    # 获取证书到期时间
    local expiry_date
    if ! expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate 2>/dev/null); then
        log_debug "无法读取证书到期时间"
        echo "INVALID"
        return 0
    fi

    # 解析到期时间
    expiry_date=$(echo "$expiry_date" | cut -d= -f2)
    local expiry_timestamp=$(date -d "$expiry_date" +%s)
    local current_timestamp=$(date +%s)
    local days_left=$(( (expiry_timestamp - current_timestamp) / 86400 ))

    log_debug "证书剩余有效期: $days_left 天"
    echo "$days_left"
}

# 检查是否需要续期
needs_renewal() {
    local domain="$1"
    local days_left

    days_left=$(check_certificate_validity "$domain")

    case "$days_left" in
        "MISSING"|"INVALID")
            log_info "证书不存在或无效，需要申请新证书"
            return 0
            ;;
        *)
            if [[ "$days_left" -le "$CERT_RENEWAL_DAYS" ]]; then
                log_info "证书将在 $days_left 天后过期，需要续期"
                return 0
            else
                log_info "证书还有 $days_left 天有效期，无需续期"
                return 1
            fi
            ;;
    esac
}

# ================================================================
# acme.sh集成函数
# ================================================================

# 初始化acme.sh环境
init_acme() {
    log_info "初始化acme.sh环境"

    # 设置Cloudflare API环境变量
    export CF_Token="${CLOUDFLARE_API_TOKEN}"
    export CF_Zone_ID="${CLOUDFLARE_ZONE_ID}"

    # 检查acme.sh是否已安装
    if ! command -v acme.sh &> /dev/null; then
        log_error "acme.sh未安装，请先安装acme.sh"
        return 1
    fi

    # 设置默认CA
    acme.sh --set-default-ca --server letsencrypt

    log_info "acme.sh环境初始化完成"
}

# 申请或续期证书
issue_certificate() {
    local domain="$1"

    log_info "开始为域名 $domain 申请/续期证书"

    # 设置acme.sh参数（使用默认存储位置）
    local acme_args=(
        --issue
        --dns dns_cf
        -d "$domain"
        --keylength ec-256  # 使用ECC证书
    )

    if [[ "$FORCE_RENEW" == "true" ]]; then
        acme_args+=(--force)
    fi

    # 执行证书申请/续期
    if acme.sh "${acme_args[@]}"; then
        log_info "证书申请/续期成功: $domain"
        return 0
    else
        log_error "证书申请/续期失败: $domain"
        return 1
    fi
}

# ================================================================
# 证书部署函数
# ================================================================

# 备份现有证书
backup_certificate() {
    local domain="$1"

    log_info "备份现有证书"

    # 创建备份目录
    mkdir -p "$BACKUP_DIR"

    local backup_file="$BACKUP_DIR/cert_backup_${domain}_$(date +%Y%m%d_%H%M%S).tar.gz"
    local cert_dirs=(
        "$PROJECT_DIR/data/nginx/certs"
        "$PROJECT_DIR/data/coturn/certs"
        "$PROJECT_DIR/data/acme"
    )

    # 创建证书备份
    if tar -czf "$backup_file" -C "$PROJECT_DIR" "${cert_dirs[@]#$PROJECT_DIR/}" 2>/dev/null; then
        log_info "证书备份完成: $backup_file"
    else
        log_warn "证书备份失败，但继续执行"
    fi
}

# 部署证书到服务
deploy_certificate() {
    local domain="$1"

    log_info "部署证书到服务"

    # 初始化证书符号链接
    if ! "$SCRIPT_DIR/init_certificate_links.sh" init; then
        log_error "证书符号链接初始化失败"
        return 1
    fi

    # 验证符号链接
    if ! "$SCRIPT_DIR/init_certificate_links.sh" status; then
        log_error "证书符号链接验证失败"
        return 1
    fi

    log_info "证书部署完成"
    return 0
}

# 重载相关服务
reload_services() {
    log_info "重载使用证书的服务"

    local services=("nginx" "coturn")
    local reload_success=true

    for service in "${services[@]}"; do
        log_info "重载服务: $service"

        if docker compose -f "$PROJECT_DIR/docker-compose.yml" restart "$service"; then
            log_info "服务 $service 重载成功"

            # 等待服务启动
            sleep 5

            # 验证服务状态
            if ! docker compose -f "$PROJECT_DIR/docker-compose.yml" ps "$service" | grep -q "Up"; then
                log_error "服务 $service 启动失败"
                reload_success=false
            fi
        else
            log_error "服务 $service 重载失败"
            reload_success=false
        fi
    done

    if [[ "$reload_success" == "true" ]]; then
        log_info "所有服务重载成功"
        return 0
    else
        log_error "部分服务重载失败"
        return 1
    fi
}

# ================================================================
# 证书验证函数
# ================================================================

# 验证证书安装
verify_certificate() {
    local domain="$1"

    log_info "验证证书安装"

    # 验证证书文件存在
    local cert_files=(
        "$PROJECT_DIR/data/nginx/certs/fullchain.cer"
        "$PROJECT_DIR/data/nginx/certs/private.key"
        "$PROJECT_DIR/data/coturn/certs/fullchain.cer"
        "$PROJECT_DIR/data/coturn/certs/private.key"
    )

    for cert_file in "${cert_files[@]}"; do
        if [[ ! -f "$cert_file" ]]; then
            log_error "证书文件不存在: $cert_file"
            return 1
        fi

        if [[ ! -s "$cert_file" ]]; then
            log_error "证书文件为空: $cert_file"
            return 1
        fi
    done

    # 验证证书有效性
    local cert_file="$PROJECT_DIR/data/nginx/certs/fullchain.cer"
    if ! openssl x509 -in "$cert_file" -noout -text >/dev/null 2>&1; then
        log_error "证书格式无效: $cert_file"
        return 1
    fi

    # 验证证书域名
    local cert_domains
    cert_domains=$(openssl x509 -in "$cert_file" -noout -text | grep -A1 "Subject Alternative Name" | tail -n1 | tr ',' '\n' | grep "DNS:" | sed 's/.*DNS://' | tr -d ' ')

    if ! echo "$cert_domains" | grep -q "$domain"; then
        log_error "证书不包含域名: $domain"
        return 1
    fi

    # 测试HTTPS连接
    if curl -s --max-time 10 "https://$domain:${HTTPS_PORT}/_matrix/client/versions" >/dev/null; then
        log_info "HTTPS连接测试成功"
    else
        log_warn "HTTPS连接测试失败，但证书文件正常"
    fi

    log_info "证书验证完成"
    return 0
}
```

### health_check.sh - 系统健康检查脚本

#### 脚本功能概述
`health_check.sh`是系统健康检查脚本，负责监控所有服务的运行状态和系统资源使用情况。

#### 核心功能模块

```bash
#!/bin/bash
# ================================================================
# Matrix Homeserver 系统健康检查脚本
# ================================================================
# 功能：全面检查Matrix Homeserver系统的健康状态
# 执行频率：每5分钟（通过cron任务）
# 检查项目：Docker服务、SSL证书、网络连接、系统资源
# ================================================================

set -euo pipefail

# ================================================================
# 脚本配置和变量
# ================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_DIR/config/deployment.env"
LOG_FILE="/var/log/matrix/health_check.log"

# 健康检查配置
DETAILED_CHECK=false       # 详细检查模式
FIX_ISSUES=false          # 自动修复问题
VERBOSE=false             # 详细输出模式

# 健康状态
OVERALL_HEALTH="HEALTHY"  # HEALTHY, WARNING, CRITICAL
CHECK_RESULTS=()          # 检查结果数组

# 阈值配置
CPU_THRESHOLD=80          # CPU使用率阈值（%）
MEMORY_THRESHOLD=85       # 内存使用率阈值（%）
DISK_THRESHOLD=90         # 磁盘使用率阈值（%）
CERT_EXPIRY_THRESHOLD=7   # 证书到期警告阈值（天）

# ================================================================
# 健康检查结果记录函数
# ================================================================

# 记录检查结果
record_check_result() {
    local component="$1"
    local status="$2"      # HEALTHY, WARNING, CRITICAL
    local message="$3"

    # 更新整体健康状态
    case "$status" in
        "CRITICAL")
            OVERALL_HEALTH="CRITICAL"
            ;;
        "WARNING")
            if [[ "$OVERALL_HEALTH" != "CRITICAL" ]]; then
                OVERALL_HEALTH="WARNING"
            fi
            ;;
    esac

    # 记录结果
    CHECK_RESULTS+=("$component:$status:$message")

    # 输出结果
    case "$status" in
        "HEALTHY")
            log_info "✅ $component: $message"
            ;;
        "WARNING")
            log_warn "⚠️  $component: $message"
            ;;
        "CRITICAL")
            log_error "❌ $component: $message"
            ;;
    esac
}

# ================================================================
# Docker服务检查函数
# ================================================================

# 检查容器是否运行
container_running() {
    local container_name="$1"

    if docker ps --format "table {{.Names}}" | grep -q "^$container_name$"; then
        return 0
    else
        return 1
    fi
}

# 检查容器健康状态
container_healthy() {
    local container_name="$1"

    local health_status
    health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")

    case "$health_status" in
        "healthy")
            return 0
            ;;
        "none")
            # 没有健康检查配置，检查容器是否运行
            container_running "$container_name"
            ;;
        *)
            return 1
            ;;
    esac
}

# 检查Docker服务状态
check_docker_services() {
    log_info "检查Docker服务状态"

    local services=("matrix_postgres" "matrix_redis" "matrix_synapse" "matrix_nginx" "matrix_coturn")
    local healthy_count=0

    for service in "${services[@]}"; do
        if container_running "$service"; then
            if container_healthy "$service"; then
                record_check_result "$service" "HEALTHY" "容器运行正常"
                healthy_count=$((healthy_count + 1))
            else
                record_check_result "$service" "WARNING" "容器运行但健康检查失败"

                if [[ "$FIX_ISSUES" == "true" ]]; then
                    log_info "尝试重启服务: $service"
                    docker compose -f "$PROJECT_DIR/docker-compose.yml" restart "${service#matrix_}"
                fi
            fi
        else
            record_check_result "$service" "CRITICAL" "容器未运行"

            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试启动服务: $service"
                docker compose -f "$PROJECT_DIR/docker-compose.yml" up -d "${service#matrix_}"
            fi
        fi
    done

    # 检查Docker守护进程
    if ! systemctl is-active --quiet docker; then
        record_check_result "docker_daemon" "CRITICAL" "Docker守护进程未运行"

        if [[ "$FIX_ISSUES" == "true" ]]; then
            log_info "尝试启动Docker守护进程"
            sudo systemctl start docker
        fi
    else
        record_check_result "docker_daemon" "HEALTHY" "Docker守护进程运行正常"
    fi

    log_info "Docker服务检查完成 ($healthy_count/${#services[@]} 服务健康)"
}

# ================================================================
# SSL证书检查函数
# ================================================================

# 检查SSL证书状态
check_certificates() {
    log_info "检查SSL证书状态"

    local domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    local cert_file="$PROJECT_DIR/data/nginx/certs/fullchain.cer"

    # 检查证书文件是否存在
    if [[ ! -f "$cert_file" ]]; then
        record_check_result "ssl_certificate" "CRITICAL" "证书文件不存在"
        return
    fi

    # 检查证书有效性
    if ! openssl x509 -in "$cert_file" -noout -checkend 0 >/dev/null 2>&1; then
        record_check_result "ssl_certificate" "CRITICAL" "证书已过期或无效"
        return
    fi

    # 检查证书到期时间
    local expiry_date
    expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
    local expiry_timestamp=$(date -d "$expiry_date" +%s)
    local current_timestamp=$(date +%s)
    local days_left=$(( (expiry_timestamp - current_timestamp) / 86400 ))

    if [[ $days_left -le $CERT_EXPIRY_THRESHOLD ]]; then
        record_check_result "ssl_certificate" "WARNING" "证书将在 $days_left 天后过期"

        if [[ "$FIX_ISSUES" == "true" ]]; then
            log_info "尝试续期证书"
            "$SCRIPT_DIR/certificate_manager.sh" --force-renew
        fi
    else
        record_check_result "ssl_certificate" "HEALTHY" "证书有效，剩余 $days_left 天"
    fi

    # 检查证书符号链接
    local cert_links=(
        "$PROJECT_DIR/data/nginx/certs/fullchain.cer"
        "$PROJECT_DIR/data/nginx/certs/private.key"
        "$PROJECT_DIR/data/coturn/certs/fullchain.cer"
        "$PROJECT_DIR/data/coturn/certs/private.key"
    )

    local broken_links=0
    for link in "${cert_links[@]}"; do
        if [[ ! -L "$link" ]] || [[ ! -f "$link" ]]; then
            broken_links=$((broken_links + 1))
        fi
    done

    if [[ $broken_links -gt 0 ]]; then
        record_check_result "certificate_links" "WARNING" "$broken_links 个证书符号链接损坏"

        if [[ "$FIX_ISSUES" == "true" ]]; then
            log_info "尝试修复证书符号链接"
            "$SCRIPT_DIR/init_certificate_links.sh" init
        fi
    else
        record_check_result "certificate_links" "HEALTHY" "证书符号链接正常"
    fi
}

# ================================================================
# 网络连接检查函数
# ================================================================

# 检查网络连接
check_network_connectivity() {
    log_info "检查网络连接"

    # 检查互联网连接
    if ping -c 1 -W 5 ******* >/dev/null 2>&1; then
        record_check_result "internet_connectivity" "HEALTHY" "互联网连接正常"
    else
        record_check_result "internet_connectivity" "CRITICAL" "互联网连接失败"
    fi

    # 检查当前公网IP
    local current_ip
    if current_ip=$(curl -s --max-time 10 https://ipv4.icanhazip.com/ 2>/dev/null); then
        record_check_result "public_ip" "HEALTHY" "公网IP: $current_ip"

        # 检查IP是否变化
        local last_ip
        last_ip=$(cat "$PROJECT_DIR/data/last_ip.txt" 2>/dev/null || echo "")
        if [[ -n "$last_ip" ]] && [[ "$current_ip" != "$last_ip" ]]; then
            record_check_result "ip_change" "WARNING" "检测到IP变化: $last_ip -> $current_ip"

            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试更新IP配置"
                "$SCRIPT_DIR/ip_watchdog.sh" --force-update
            fi
        fi
    else
        record_check_result "public_ip" "WARNING" "无法获取公网IP"
    fi

    # 检查DNS解析
    local matrix_domain="${SUBDOMAIN_MATRIX}.${DOMAIN}"
    if nslookup "$matrix_domain" >/dev/null 2>&1; then
        record_check_result "dns_resolution" "HEALTHY" "DNS解析正常: $matrix_domain"
    else
        record_check_result "dns_resolution" "CRITICAL" "DNS解析失败: $matrix_domain"
    fi
}

# ================================================================
# Matrix服务检查函数
# ================================================================

# 检查Matrix API
check_matrix_services() {
    log_info "检查Matrix服务"

    local matrix_url="https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}"

    # 检查客户端API
    if curl -s --max-time 10 -k "$matrix_url/_matrix/client/versions" >/dev/null; then
        record_check_result "matrix_client_api" "HEALTHY" "客户端API响应正常"
    else
        record_check_result "matrix_client_api" "CRITICAL" "客户端API无响应"
    fi

    # 检查联邦API
    if curl -s --max-time 10 -k "$matrix_url/_matrix/federation/v1/version" >/dev/null; then
        record_check_result "matrix_federation_api" "HEALTHY" "联邦API响应正常"
    else
        record_check_result "matrix_federation_api" "CRITICAL" "联邦API无响应"
    fi

    # 检查.well-known发现（外部指路牌）
    if curl -s --max-time 10 "https://$DOMAIN/.well-known/matrix/client" >/dev/null; then
        record_check_result "wellknown_client" "HEALTHY" "客户端发现正常"
    else
        record_check_result "wellknown_client" "WARNING" "客户端发现失败"
    fi

    if curl -s --max-time 10 "https://$DOMAIN/.well-known/matrix/server" >/dev/null; then
        record_check_result "wellknown_server" "HEALTHY" "服务器发现正常"
    else
        record_check_result "wellknown_server" "WARNING" "服务器发现失败"
    fi
}

# ================================================================
# 系统资源检查函数
# ================================================================

# 检查系统资源使用
check_system_resources() {
    log_info "检查系统资源使用"

    # 检查CPU使用率
    local cpu_usage
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    cpu_usage=${cpu_usage%.*}  # 去除小数部分

    if [[ $cpu_usage -gt $CPU_THRESHOLD ]]; then
        record_check_result "cpu_usage" "WARNING" "CPU使用率过高: ${cpu_usage}%"
    else
        record_check_result "cpu_usage" "HEALTHY" "CPU使用率正常: ${cpu_usage}%"
    fi

    # 检查内存使用率
    local memory_info
    memory_info=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')

    if [[ $memory_info -gt $MEMORY_THRESHOLD ]]; then
        record_check_result "memory_usage" "WARNING" "内存使用率过高: ${memory_info}%"
    else
        record_check_result "memory_usage" "HEALTHY" "内存使用率正常: ${memory_info}%"
    fi

    # 检查磁盘使用率
    local disk_usage
    disk_usage=$(df "$PROJECT_DIR" | awk 'NR==2{print $5}' | sed 's/%//')

    if [[ $disk_usage -gt $DISK_THRESHOLD ]]; then
        record_check_result "disk_usage" "WARNING" "磁盘使用率过高: ${disk_usage}%"
    else
        record_check_result "disk_usage" "HEALTHY" "磁盘使用率正常: ${disk_usage}%"
    fi

    # 检查系统负载
    local load_avg
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_ratio=$(echo "$load_avg $cpu_cores" | awk '{printf "%.1f", $1/$2}')

    if (( $(echo "$load_ratio > 1.5" | bc -l) )); then
        record_check_result "system_load" "WARNING" "系统负载过高: $load_avg (${load_ratio}x CPU核心数)"
    else
        record_check_result "system_load" "HEALTHY" "系统负载正常: $load_avg"
    fi
}

# ================================================================
# 健康报告生成函数
# ================================================================

# 生成健康检查报告
generate_health_report() {
    log_info "生成健康检查报告"

    local report_file="/tmp/matrix_health_report_$(date +%Y%m%d_%H%M%S).txt"

    cat > "$report_file" << EOF
Matrix Homeserver 健康检查报告
生成时间: $(date)
整体状态: $OVERALL_HEALTH

详细检查结果:
EOF

    # 按状态分类显示结果
    local healthy_count=0
    local warning_count=0
    local critical_count=0

    for result in "${CHECK_RESULTS[@]}"; do
        IFS=':' read -r component status message <<< "$result"

        case "$status" in
            "HEALTHY")
                echo "✅ $component: $message" >> "$report_file"
                healthy_count=$((healthy_count + 1))
                ;;
            "WARNING")
                echo "⚠️  $component: $message" >> "$report_file"
                warning_count=$((warning_count + 1))
                ;;
            "CRITICAL")
                echo "❌ $component: $message" >> "$report_file"
                critical_count=$((critical_count + 1))
                ;;
        esac
    done

    cat >> "$report_file" << EOF

统计信息:
- 健康项目: $healthy_count
- 警告项目: $warning_count
- 严重问题: $critical_count
- 总检查项: ${#CHECK_RESULTS[@]}

EOF

    # 输出报告摘要
    log_info "健康检查完成"
    log_info "健康项目: $healthy_count, 警告: $warning_count, 严重: $critical_count"

    if [[ "$VERBOSE" == "true" ]]; then
        cat "$report_file"
    fi

    # 清理临时报告文件
    rm -f "$report_file"
}
```

---

## 👨‍💼 管理脚本详解

### admin.sh - Matrix管理工具脚本

#### 脚本功能概述
`admin.sh`是Matrix Homeserver的管理工具脚本，提供用户管理、房间管理、数据库维护等功能。

#### 核心功能模块

```bash
#!/bin/bash
# ================================================================
# Matrix Homeserver 管理工具脚本
# ================================================================
# 功能：提供Matrix Homeserver的日常管理功能
# 用途：用户管理、房间管理、数据库维护、系统监控
# ================================================================

# 用户管理功能
user_management() {
    local action="$1"
    shift

    case "$action" in
        "create")
            create_user "$@"
            ;;
        "delete")
            delete_user "$@"
            ;;
        "list")
            list_users "$@"
            ;;
        "reset-password")
            reset_user_password "$@"
            ;;
        "make-admin")
            make_user_admin "$@"
            ;;
        *)
            echo "用法: $0 user {create|delete|list|reset-password|make-admin}"
            ;;
    esac
}

# 创建用户
create_user() {
    local username="$1"
    local is_admin="${2:-false}"

    log_info "创建用户: $username"

    if [[ "$is_admin" == "true" ]] || [[ "$is_admin" == "--admin" ]]; then
        docker compose -f "$PROJECT_DIR/docker-compose.yml" exec synapse \
            register_new_matrix_user -c /data/homeserver.yaml -u "$username" -a http://localhost:8008
    else
        docker compose -f "$PROJECT_DIR/docker-compose.yml" exec synapse \
            register_new_matrix_user -c /data/homeserver.yaml -u "$username" http://localhost:8008
    fi
}

# 房间管理功能
room_management() {
    local action="$1"
    shift

    case "$action" in
        "list")
            list_rooms "$@"
            ;;
        "delete")
            delete_room "$@"
            ;;
        "purge")
            purge_room_history "$@"
            ;;
        *)
            echo "用法: $0 room {list|delete|purge}"
            ;;
    esac
}

# 数据库管理功能
database_management() {
    local action="$1"
    shift

    case "$action" in
        "backup")
            backup_database "$@"
            ;;
        "vacuum")
            vacuum_database "$@"
            ;;
        "stats")
            show_database_stats "$@"
            ;;
        *)
            echo "用法: $0 db {backup|vacuum|stats}"
            ;;
    esac
}
```

---

## 🔧 脚本执行流程

### 脚本间的协作关系

```mermaid
graph TD
    A[setup.sh 初始化部署] --> B[certificate_manager.sh 证书管理]
    A --> C[ip_watchdog.sh IP监控]
    A --> D[health_check.sh 健康检查]

    B --> E[init_certificate_links.sh 证书链接]
    C --> F[RouterOS API 客户端]
    D --> G[admin.sh 管理工具]

    H[backup.sh 备份脚本] --> I[数据备份]
    G --> J[用户管理]
    G --> K[房间管理]
    G --> L[数据库维护]

    M[Cron 定时任务] --> C
    M --> B
    M --> D
    M --> H
```

### 典型执行流程

1. **初始部署流程**：
   ```bash
   setup.sh → 环境检查 → 依赖安装 → 配置生成 → 证书申请 → 服务启动
   ```

2. **日常运维流程**：
   ```bash
   ip_watchdog.sh (每分钟) → 检查IP变化 → 更新DNS → 重启Coturn
   certificate_manager.sh (每天) → 检查证书 → 续期证书 → 重载服务
   health_check.sh (每5分钟) → 检查服务 → 记录状态 → 自动修复
   backup.sh (每天) → 备份数据 → 清理旧备份
   ```

3. **故障处理流程**：
   ```bash
   health_check.sh → 发现问题 → 自动修复 → 记录日志 → 发送告警
   ```

---

## 🐛 脚本调试技巧

### 调试模式启用

```bash
# 启用详细输出
./scripts/setup.sh --verbose

# 启用Bash调试模式
bash -x ./scripts/setup.sh

# 检查脚本语法
bash -n ./scripts/setup.sh
```

### 日志分析

```bash
# 查看脚本执行日志
tail -f /var/log/matrix/setup.log
tail -f /var/log/matrix/ip_watchdog.log
tail -f /var/log/matrix/certificate_manager.log
tail -f /var/log/matrix/health_check.log

# 过滤错误信息
grep "ERROR" /var/log/matrix/*.log

# 查看最近的执行记录
grep "$(date +%Y-%m-%d)" /var/log/matrix/*.log
```

### 常见问题排查

1. **权限问题**：
   ```bash
   # 检查文件权限
   ls -la /opt/matrix/scripts/

   # 修复执行权限
   chmod +x /opt/matrix/scripts/*.sh
   ```

2. **环境变量问题**：
   ```bash
   # 检查环境变量加载
   source /opt/matrix/config/deployment.env
   env | grep DOMAIN
   ```

3. **依赖问题**：
   ```bash
   # 检查命令是否存在
   command -v docker
   command -v acme.sh
   command -v python3
   ```

通过这份详细的脚本功能说明，技术新手可以深入理解每个脚本的工作原理和执行逻辑，从而更好地管理和维护Matrix Homeserver部署。
```
```
```
```
