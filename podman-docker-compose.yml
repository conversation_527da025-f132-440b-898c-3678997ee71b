# Matrix Homeserver Podman Compose配置文件
# 完全适配Podman的容器编排配置
# 
# 主要特点：
# 1. 移除Docker特有的功能
# 2. 优化Podman网络配置
# 3. 调整健康检查语法
# 4. 支持无根容器运行
# 5. 详细的中文注释说明

version: '3.8'

# ================================================================
# 网络定义
# ================================================================
networks:
  matrix_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

# ================================================================
# 数据卷定义
# ================================================================
volumes:
  # PostgreSQL数据卷
  postgres_data:
    driver: local
  
  # Redis数据卷
  redis_data:
    driver: local
  
  # Synapse数据卷
  synapse_data:
    driver: local
  
  # 日志数据卷
  nginx_logs:
    driver: local
  coturn_logs:
    driver: local

# ================================================================
# 服务定义
# ================================================================
services:
  
  # ================================================================
  # PostgreSQL数据库服务
  # ================================================================
  db:
    # 镜像配置
    image: postgres:15-alpine
    container_name: matrix_postgres
    restart: unless-stopped
    
    # 环境变量配置
    # 这些变量控制PostgreSQL的初始化和运行
    environment:
      # 数据库基本配置
      POSTGRES_DB: ${DB_NAME:-synapse}                    # 数据库名称
      POSTGRES_USER: ${DB_USER:-synapse}                  # 数据库用户名
      POSTGRES_PASSWORD: ${DB_PASSWORD}                   # 数据库密码（必须设置）
      
      # 数据库初始化参数
      # 这些参数确保数据库使用正确的编码和排序规则
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      
      # PostgreSQL运行时配置
      POSTGRES_HOST_AUTH_METHOD: md5                      # 认证方法
    
    # 数据卷挂载
    volumes:
      # 数据持久化目录
      - postgres_data:/var/lib/postgresql/data
      
      # 数据库初始化脚本
      # 这个脚本会在数据库首次启动时执行
      - ./config/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      
      # PostgreSQL配置文件（可选）
      # - ./config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    
    # 网络配置
    networks:
      - matrix_network
    
    # 端口配置（仅内部访问，不对外暴露）
    # ports:
    #   - "127.0.0.1:5432:5432"
    
    # 资源限制
    # 根据服务器配置调整这些值
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-1g}             # 内存限制
          cpus: '${POSTGRES_CPU_LIMIT:-1}'                 # CPU限制
        reservations:
          memory: 256m                                     # 内存预留
          cpus: '0.25'                                     # CPU预留
    
    # 健康检查
    # Podman兼容的健康检查语法
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-synapse} -d ${DB_NAME:-synapse} || exit 1"]
      interval: 30s                                        # 检查间隔
      timeout: 10s                                         # 超时时间
      retries: 3                                           # 重试次数
      start_period: 30s                                    # 启动等待时间
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"                                    # 单个日志文件最大大小
        max-file: "3"                                      # 保留的日志文件数量

  # ================================================================
  # Redis缓存服务
  # ================================================================
  redis:
    # 镜像配置
    image: redis:7-alpine
    container_name: matrix_redis
    restart: unless-stopped
    
    # Redis启动命令
    # 配置Redis的运行参数
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    
    # 数据卷挂载
    volumes:
      # Redis数据持久化
      - redis_data:/data
      
      # Redis配置文件（可选）
      # - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    # 网络配置
    networks:
      - matrix_network
    
    # 端口配置（仅内部访问）
    # ports:
    #   - "127.0.0.1:6379:6379"
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-512m}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'
        reservations:
          memory: 64m
          cpus: '0.1'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ================================================================
  # Synapse Matrix服务器
  # ================================================================
  synapse:
    # 镜像配置
    image: matrixdotorg/synapse:latest
    container_name: matrix_synapse
    restart: unless-stopped
    
    # 注意：Podman不完全支持depends_on条件
    # 需要手动确保数据库和Redis先启动
    # depends_on:
    #   - db
    #   - redis
    
    # 环境变量配置
    environment:
      # Synapse基本配置
      SYNAPSE_SERVER_NAME: ${DOMAIN}                       # Matrix服务器域名
      SYNAPSE_REPORT_STATS: "no"                          # 不发送使用统计
      SYNAPSE_CONFIG_PATH: /data/homeserver.yaml          # 配置文件路径
      
      # 数据库连接配置
      SYNAPSE_CONFIG_DIR: /data                           # 配置目录
      
      # 日志配置
      SYNAPSE_LOG_LEVEL: INFO                             # 日志级别
    
    # 数据卷挂载
    volumes:
      # Synapse数据目录
      - synapse_data:/data
      
      # 配置文件
      - ./config/homeserver.yaml:/data/homeserver.yaml:ro
      - ./config/log.config:/data/log.config:ro
      
      # 媒体存储目录
      - ./data/synapse/media:/data/media_store
      
      # 日志目录
      - ./data/synapse/logs:/data/logs
    
    # 网络配置
    networks:
      - matrix_network
    
    # 端口配置（仅内部访问）
    # ports:
    #   - "127.0.0.1:8008:8008"
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${SYNAPSE_MEMORY_LIMIT:-2g}
          cpus: '${SYNAPSE_CPU_LIMIT:-2}'
        reservations:
          memory: 512m
          cpus: '0.5'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

  # ================================================================
  # Nginx反向代理服务
  # ================================================================
  nginx:
    # 镜像配置
    image: nginx:alpine
    container_name: matrix_nginx
    restart: unless-stopped
    
    # 端口映射
    # 绑定到本地接口，通过路由器端口转发对外提供服务
    ports:
      - "127.0.0.1:${HTTPS_PORT:-8448}:${HTTPS_PORT:-8448}"
    
    # 数据卷挂载
    volumes:
      # Nginx配置文件
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/matrix.conf:/etc/nginx/conf.d/matrix.conf:ro
      
      # SSL证书目录
      - ./data/nginx/certs:/etc/nginx/certs:ro
      
      # 日志目录
      - nginx_logs:/var/log/nginx
      
      # 媒体文件目录（只读）
      - ./data/synapse/media:/var/www/matrix/media:ro
      
      # acme.sh证书目录（只读）
      - /root/.acme.sh:/root/.acme.sh:ro
    
    # 网络配置
    networks:
      - matrix_network
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-256m}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'
        reservations:
          memory: 32m
          cpus: '0.1'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ================================================================
  # Coturn TURN/STUN服务器
  # ================================================================
  coturn:
    # 镜像配置
    image: coturn/coturn:latest
    container_name: matrix_coturn
    restart: unless-stopped
    
    # 网络配置
    # 使用端口映射替代host模式（Podman无根容器兼容）
    ports:
      # STUN/TURN端口
      - "3478:3478/tcp"
      - "3478:3478/udp"
      
      # TURNS端口（TLS加密）
      - "5349:5349/tcp"
      
      # TURN UDP端口范围
      # 注意：这个范围很大，确保路由器已正确配置端口转发
      - "49152-65535:49152-65535/udp"
    
    # 数据卷挂载
    volumes:
      # Coturn配置文件
      - ./config/turnserver.conf:/etc/coturn/turnserver.conf:ro
      
      # SSL证书目录
      - ./data/coturn/certs:/etc/coturn/certs:ro
      
      # 日志目录
      - coturn_logs:/var/log/coturn
      
      # acme.sh证书目录（只读）
      - /root/.acme.sh:/root/.acme.sh:ro
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${COTURN_MEMORY_LIMIT:-512m}
          cpus: '${COTURN_CPU_LIMIT:-1}'
        reservations:
          memory: 64m
          cpus: '0.1'
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
