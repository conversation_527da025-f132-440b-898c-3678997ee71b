# Matrix Homeserver Podman版本配置文件模板
# 
# 说明：
# 1. 复制此文件为 deployment.env 并根据实际情况修改
# 2. 所有以 # 开头的行为注释，不会被读取
# 3. 配置项格式：KEY="value"，注意使用双引号
# 4. 敏感信息（如密码）请使用强密码
# 5. 本配置文件专门针对Podman进行了优化

# ================================================================
# 基础域名配置
# ================================================================

# 主域名（必须配置）
# 示例：example.com
DOMAIN="your-domain.com"

# Matrix服务子域名（必须配置）
# 完整的Matrix服务地址将是：matrix.your-domain.com
SUBDOMAIN_MATRIX="matrix"

# HTTPS服务端口（推荐使用Matrix官方标准端口）
# 8448是Matrix联邦协议的官方标准端口
HTTPS_PORT="8448"

# ================================================================
# 容器引擎配置（Podman专用）
# ================================================================

# 容器引擎类型
CONTAINER_ENGINE="podman"

# Compose命令
COMPOSE_COMMAND="podman-compose"

# 无根容器配置
ENABLE_ROOTLESS="true"

# Podman socket路径（无根模式）
PODMAN_SOCKET_PATH="/run/user/$(id -u)/podman/podman.sock"

# 网络配置
PODMAN_NETWORK_MODE="bridge"

# 是否启用host网络模式（仅Coturn，需要root权限）
ENABLE_HOST_NETWORK="false"

# 存储配置
PODMAN_STORAGE_DRIVER="overlay"
PODMAN_STORAGE_ROOT="/home/<USER>/.local/share/containers/storage"

# ================================================================
# 数据库配置
# ================================================================

# PostgreSQL数据库配置
DB_NAME="synapse"
DB_USER="synapse"

# 数据库密码（必须设置强密码）
# 生成强密码命令：openssl rand -base64 32
DB_PASSWORD="your_secure_database_password_here"

# 数据库连接配置
DB_HOST="db"                    # 容器名称
DB_PORT="5432"
DB_SSLMODE="disable"           # 内网连接，禁用SSL

# ================================================================
# Redis缓存配置
# ================================================================

# Redis基本配置
REDIS_HOST="redis"             # 容器名称
REDIS_PORT="6379"
REDIS_PASSWORD=""              # 留空表示无密码

# Redis内存配置
REDIS_MAXMEMORY="256mb"
REDIS_MAXMEMORY_POLICY="allkeys-lru"

# ================================================================
# Cloudflare DNS配置
# ================================================================

# Cloudflare API配置（必须配置）
# 获取方法：登录Cloudflare → 我的个人资料 → API令牌
CLOUDFLARE_API_TOKEN="your_cloudflare_api_token_here"

# Cloudflare Zone ID（必须配置）
# 获取方法：Cloudflare域名管理页面右侧
CLOUDFLARE_ZONE_ID="your_cloudflare_zone_id_here"

# Cloudflare账户邮箱
CLOUDFLARE_EMAIL="<EMAIL>"

# DNS记录配置
DNS_RECORD_NAME="${SUBDOMAIN_MATRIX}.${DOMAIN}"
DNS_RECORD_TTL="300"           # DNS记录TTL（秒）

# ================================================================
# SSL证书配置
# ================================================================

# 证书类型（ECC推荐，RSA兼容性更好）
CERT_TYPE="ECC"                # ECC 或 RSA

# ECC密钥长度
ECC_KEY_LENGTH="ec-256"        # ec-256, ec-384, ec-521

# RSA密钥长度
RSA_KEY_LENGTH="2048"          # 2048, 3072, 4096

# 证书自动更新
ENABLE_CERT_AUTO_RENEWAL="true"
CERT_RENEWAL_DAYS="30"         # 提前多少天更新证书

# ================================================================
# TURN服务器配置（Coturn）
# ================================================================

# Coturn共享密钥（必须设置强密钥）
# 生成命令：openssl rand -base64 32
COTURN_SHARED_SECRET="your_coturn_shared_secret_here"

# TURN服务端口
TURN_PORT="3478"               # STUN/TURN端口
TURNS_PORT="5349"              # TURNS端口（TLS加密）

# TURN UDP端口范围
COTURN_MIN_PORT="49152"
COTURN_MAX_PORT="65535"

# Coturn配置
COTURN_REALM="${SUBDOMAIN_MATRIX}.${DOMAIN}"
COTURN_EXTERNAL_IP=""          # 留空自动获取

# ================================================================
# Synapse配置
# ================================================================

# Synapse管理员用户
SYNAPSE_ADMIN_USER="admin"

# Synapse密钥（必须设置强密钥）
# 生成命令：openssl rand -base64 32
SYNAPSE_FORM_SECRET="your_synapse_form_secret_here"
SYNAPSE_MACAROON_SECRET="your_synapse_macaroon_secret_here"

# 用户注册配置
ENABLE_REGISTRATION="false"    # 是否允许新用户注册
REGISTRATION_SHARED_SECRET=""  # 注册共享密钥（如果启用注册）

# 媒体配置
MAX_UPLOAD_SIZE="50M"          # 最大上传文件大小
MEDIA_RETENTION_LOCAL="365d"   # 本地媒体保留时间
MEDIA_RETENTION_REMOTE="90d"   # 远程媒体保留时间

# ================================================================
# RouterOS API配置（动态IP监控）
# ================================================================

# RouterOS设备连接信息
ROUTEROS_HOST="***********"
ROUTEROS_PORT="8728"
ROUTEROS_USER="matrix-api"
ROUTEROS_PASSWORD="your_routeros_password_here"
ROUTEROS_WAN_INTERFACE="WAN"
ROUTEROS_TIMEOUT="10"

# 启用RouterOS API监控
ENABLE_ROUTEROS_MONITORING="true"

# IP监控配置
IP_CHECK_INTERVAL="60"         # IP检查间隔（秒）
IP_UPDATE_MAX_RETRIES="3"      # 最大重试次数
IP_UPDATE_RETRY_DELAY="10"     # 重试延迟（秒）

# ================================================================
# 性能调优配置（Podman优化）
# ================================================================

# PostgreSQL容器资源限制
POSTGRES_MEMORY_LIMIT="1g"
POSTGRES_CPU_LIMIT="1"

# Redis容器资源限制
REDIS_MEMORY_LIMIT="512m"
REDIS_CPU_LIMIT="0.5"

# Synapse容器资源限制
SYNAPSE_MEMORY_LIMIT="2g"
SYNAPSE_CPU_LIMIT="2"

# Nginx容器资源限制
NGINX_MEMORY_LIMIT="256m"
NGINX_CPU_LIMIT="0.5"

# Coturn容器资源限制
COTURN_MEMORY_LIMIT="512m"
COTURN_CPU_LIMIT="1"

# ================================================================
# 日志配置
# ================================================================

# 日志级别
LOG_LEVEL="INFO"               # DEBUG, INFO, WARN, ERROR

# 日志文件路径
LOG_DIR="/var/log/matrix"

# 日志轮转配置
LOG_MAX_SIZE="10M"             # 单个日志文件最大大小
LOG_MAX_FILES="5"              # 保留的日志文件数量

# ================================================================
# 监控和健康检查配置
# ================================================================

# 启用健康检查
ENABLE_HEALTH_CHECK="true"

# 健康检查间隔
HEALTH_CHECK_INTERVAL="300"    # 5分钟

# 启用自动修复
ENABLE_AUTO_REPAIR="false"

# 监控告警配置
ENABLE_MONITORING_ALERTS="false"
ALERT_EMAIL=""                 # 告警邮箱

# ================================================================
# 备份配置
# ================================================================

# 启用自动备份
ENABLE_AUTO_BACKUP="true"

# 备份目录
BACKUP_DIR="/opt/matrix/backup"

# 备份保留天数
BACKUP_RETENTION_DAYS="30"

# 备份时间（cron格式）
BACKUP_SCHEDULE="0 3 * * *"    # 每天凌晨3点

# ================================================================
# 安全配置
# ================================================================

# 防火墙配置
ENABLE_UFW="true"

# 失败登录保护
ENABLE_FAIL2BAN="true"

# SSL安全配置
SSL_PROTOCOLS="TLSv1.2 TLSv1.3"
SSL_CIPHERS="ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384"

# ================================================================
# 开发和调试配置
# ================================================================

# 调试模式
DEBUG_MODE="false"

# 详细日志
VERBOSE_LOGGING="false"

# 测试模式
TEST_MODE="false"

# ================================================================
# 注意事项
# ================================================================

# 1. 所有密码和密钥都必须设置为强密码
# 2. 域名配置必须与实际域名匹配
# 3. Cloudflare API配置必须正确
# 4. RouterOS配置必须与实际设备匹配
# 5. 资源限制根据服务器配置调整
# 6. 备份配置根据需求调整
# 7. 安全配置建议保持启用状态
