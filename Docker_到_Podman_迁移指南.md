# Matrix Homeserver: Docker到Podman迁移指南

## 📋 迁移概述

本指南详细说明如何将现有的Docker部署迁移到Podman环境，包括配置调整、命令对比和最佳实践。

---

## 🔄 核心差异对比

### 架构差异

| 特性 | Docker | Podman |
|------|--------|--------|
| **守护进程** | 需要dockerd守护进程 | 无守护进程架构 |
| **权限要求** | 需要root权限 | 支持无根容器 |
| **安全模型** | 集中式守护进程 | 分布式，更安全 |
| **systemd集成** | 第三方支持 | 原生支持 |
| **Pod概念** | 不支持 | 原生支持Kubernetes风格Pod |

### 命令对比

| 功能 | Docker | Podman |
|------|--------|--------|
| **运行容器** | `docker run` | `podman run` |
| **构建镜像** | `docker build` | `podman build` |
| **查看容器** | `docker ps` | `podman ps` |
| **容器编排** | `docker-compose` | `podman pod` (推荐) |
| **网络管理** | `docker network` | `podman network` |
| **卷管理** | `docker volume` | `podman volume` |

---

## 🛠️ 配置文件迁移

### 1. docker-compose.yml 到 Podman Pod

#### 原始Docker Compose配置
```yaml
version: '3.8'
services:
  synapse:
    image: matrixdotorg/synapse:latest
    depends_on:
      db:
        condition: service_healthy
    network_mode: host
    volumes:
      - ./data/synapse:/data
```

#### Podman Pod等效配置
```bash
# 创建Pod
podman pod create --name matrix-pod --publish 8448:8448

# 启动容器（无需depends_on，手动控制启动顺序）
podman run -d --pod matrix-pod --name matrix-synapse \
  -v ./data/synapse:/data:Z \
  matrixdotorg/synapse:latest
```

### 2. 网络配置迁移

#### Docker网络配置
```yaml
networks:
  matrix_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

#### Podman网络配置
```bash
# Podman Pod内部自动处理网络
# 容器间通过localhost通信
# 无需额外网络配置
```

### 3. 卷挂载迁移

#### Docker卷挂载
```yaml
volumes:
  - ./data/postgres:/var/lib/postgresql/data
  - ./config/homeserver.yaml:/data/homeserver.yaml:ro
```

#### Podman卷挂载
```bash
# 添加SELinux标签 :Z 用于共享卷
-v ./data/postgres:/var/lib/postgresql/data:Z
-v ./config/homeserver.yaml:/data/homeserver.yaml:ro,Z
```

---

## 🔧 迁移步骤

### 第一步：停止Docker服务

```bash
# 停止Docker Compose服务
docker-compose down

# 备份数据
tar -czf docker_backup.tar.gz data/ config/

# 停止Docker守护进程（可选）
sudo systemctl stop docker
sudo systemctl disable docker
```

### 第二步：安装Podman

```bash
# Debian/Ubuntu
sudo apt update
sudo apt install -y podman buildah skopeo

# 配置无根容器
echo 'matrix:100000:65536' | sudo tee -a /etc/subuid
echo 'matrix:100000:65536' | sudo tee -a /etc/subgid
sudo loginctl enable-linger matrix
```

### 第三步：迁移镜像

```bash
# 导出Docker镜像
docker save matrixdotorg/synapse:latest > synapse.tar
docker save postgres:15-alpine > postgres.tar
docker save redis:7-alpine > redis.tar
docker save nginx:alpine > nginx.tar
docker save coturn/coturn:latest > coturn.tar

# 导入到Podman
podman load < synapse.tar
podman load < postgres.tar
podman load < redis.tar
podman load < nginx.tar
podman load < coturn.tar

# 或者直接拉取（推荐）
podman pull matrixdotorg/synapse:latest
podman pull postgres:15-alpine
podman pull redis:7-alpine
podman pull nginx:alpine
podman pull coturn/coturn:latest
```

### 第四步：调整配置文件

```bash
# 更新homeserver.yaml中的主机地址
# Docker: host: db
# Podman: host: 127.0.0.1 (Pod内部通信)

sed -i 's/host: db/host: 127.0.0.1/' config/homeserver.yaml
sed -i 's/host: redis/host: 127.0.0.1/' config/homeserver.yaml
```

### 第五步：使用Podman Pod部署

```bash
# 使用提供的管理脚本
./scripts/manage-pod.sh deploy
```

---

## ⚠️ 重要注意事项

### 1. SELinux标签

Podman在支持SELinux的系统上需要正确的标签：

```bash
# 共享卷使用 :Z 标签
-v /host/path:/container/path:Z

# 只读挂载添加 ro 标签
-v /host/path:/container/path:ro,Z
```

### 2. 网络模式差异

| Docker | Podman | 说明 |
|--------|--------|------|
| `network_mode: host` | 端口映射 | Podman无根模式下不支持host网络 |
| 自定义网络 | Pod内部网络 | Pod内容器自动共享网络 |
| 容器间通信 | localhost | Pod内容器通过localhost通信 |

### 3. 健康检查

Docker Compose的健康检查条件在Podman中不支持：

```yaml
# Docker (不支持)
depends_on:
  db:
    condition: service_healthy

# Podman解决方案
# 使用脚本控制启动顺序
# 或使用systemd服务依赖
```

### 4. 资源限制

```bash
# Docker
deploy:
  resources:
    limits:
      memory: 2g

# Podman
--memory=2g
```

---

## 🔍 验证迁移

### 1. 服务状态检查

```bash
# 检查Pod状态
podman pod ps

# 检查容器状态
podman ps --pod

# 检查服务功能
curl -k https://localhost:8448/_matrix/client/versions
```

### 2. 数据完整性验证

```bash
# 检查数据库
podman exec matrix-postgres psql -U synapse -d synapse -c "\dt"

# 检查媒体文件
ls -la data/synapse/media_store/

# 检查配置文件
podman exec matrix-synapse cat /data/homeserver.yaml
```

### 3. 性能对比

```bash
# 查看资源使用
podman stats

# 对比启动时间
time ./scripts/manage-pod.sh start
```

---

## 🚀 Podman优势

### 1. 安全性提升

- **无根容器**: 默认以非特权用户运行
- **无守护进程**: 减少攻击面
- **SELinux集成**: 更好的安全隔离

### 2. 系统集成

- **systemd原生支持**: 更好的服务管理
- **cgroup v2支持**: 更精确的资源控制
- **用户会话**: 支持用户级容器服务

### 3. 兼容性

- **Docker镜像兼容**: 可直接使用Docker镜像
- **OCI标准**: 符合开放容器标准
- **Kubernetes兼容**: Pod概念与K8s一致

---

## 🔄 回滚方案

如果需要回滚到Docker：

```bash
# 停止Podman服务
./scripts/manage-pod.sh stop

# 启动Docker服务
sudo systemctl start docker

# 恢复Docker Compose
docker-compose up -d

# 恢复数据（如果需要）
tar -xzf docker_backup.tar.gz
```

---

## 📚 参考资源

- **Podman官方文档**: https://podman.io/docs/
- **Docker到Podman迁移**: https://podman.io/docs/migration
- **无根容器指南**: https://rootlesscontaine.rs/
- **SELinux容器指南**: https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/8/html/building_running_and_managing_containers/

---

## ✅ 迁移检查清单

- [ ] 备份Docker数据和配置
- [ ] 安装Podman环境
- [ ] 配置无根容器支持
- [ ] 迁移容器镜像
- [ ] 调整配置文件
- [ ] 部署Podman Pod
- [ ] 验证服务功能
- [ ] 配置systemd服务
- [ ] 设置监控和备份
- [ ] 文档更新

完成以上步骤后，您的Matrix Homeserver将成功从Docker迁移到Podman环境！
